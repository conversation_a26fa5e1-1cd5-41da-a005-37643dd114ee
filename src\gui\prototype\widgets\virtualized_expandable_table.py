"""
虚拟化可展开表格组件 - Phase 4 核心交互功能增强

基于creative-core-algorithms.md的虚拟化展开算法设计，
实现支持大数据量的智能表格展开/折叠系统。

主要特性:
- 虚拟化渲染：只渲染可见区域，支持大数据量
- 智能展开：流畅的展开/折叠动画效果 
- 表格内编辑：双击编辑、Enter确认、Esc取消 (P4-001)
- 多选批量操作：Ctrl+Click、Shift+Click、8种批量功能 (P4-002)
- 右键菜单系统：上下文感知菜单、动态菜单项 (P4-003)
- 性能优化：展开响应时间<200ms，60fps动画目标
- Material Design：现代化视觉效果和交互反馈

技术架构:
- VirtualizedExpandableTable: 主表格组件
- ExpandableTableItem: 可展开行项
- ExpandableTableModel: 数据模型
- TableAnimationManager: 动画管理器
- TableCellEditor: 单元格编辑器 (P4-001)
- TableContextMenu: 右键菜单系统 (P4-003)

作者: PyQt5重构项目组
创建时间: 2025-06-19 16:10
更新时间: 2025-06-19 18:10 (P4-003 右键菜单系统)
"""

import sys
import math
import time
from typing import List, Dict, Any, Optional, Union, Tuple, Callable
from enum import Enum
from dataclasses import dataclass

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QSizePolicy, QFrame, QPushButton, QLabel, QLineEdit, QSpinBox,
    QComboBox, QDateEdit, QDoubleSpinBox, QSpinBox, QCheckBox, 
    QTextEdit, QStyledItemDelegate, QMenu, QAction, QWidgetAction,
    QMessageBox, QToolTip, QShortcut
)
from PyQt5.QtCore import (
    Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect, QSize,
    pyqtSignal, QObject, QVariant, QModelIndex, QAbstractTableModel,
    QEvent, QDate, QDateTime, QPoint, QMimeData
)
from PyQt5.QtGui import (
    QPainter, QPen, QBrush, QColor, QFont, QFontMetrics, QPalette,
    QLinearGradient, QPainterPath, QPolygonF, QMouseEvent, QPaintEvent,
    QKeyEvent, QValidator, QIntValidator, QDoubleValidator, QIcon,
    QContextMenuEvent, QPixmap, QDrag
)

# 修复导入问题
try:
    from src.utils.log_config import setup_logger
    from src.modules.data_import.header_edit_manager import HeaderEditManager
    from src.modules.data_import.config_sync_manager import ConfigSyncManager
except ImportError:
    # 如果无法导入，使用标准日志
    import logging
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    # 如果无法导入，创建Mock类
    class HeaderEditManager:
        def __init__(self, *args, **kwargs):
            pass
        def show_edit_dialog(self, *args, **kwargs):
            return False
    
    class ConfigSyncManager:
        def __init__(self, *args, **kwargs):
            pass


class ExpandState(Enum):
    """表格行展开状态枚举"""
    COLLAPSED = "collapsed"    # 折叠状态
    EXPANDING = "expanding"    # 展开中
    EXPANDED = "expanded"      # 已展开
    COLLAPSING = "collapsing"  # 折叠中


class ContextMenuType(Enum):
    """右键菜单类型枚举"""
    SINGLE_ROW = "single_row"          # 单行菜单
    MULTI_ROW = "multi_row"            # 多行菜单
    CELL_EDIT = "cell_edit"            # 单元格编辑菜单
    HEADER = "header"                  # 表头菜单
    EMPTY_AREA = "empty_area"          # 空白区域菜单
    EXPANDED_ROW = "expanded_row"      # 已展开行菜单
    COLLAPSED_ROW = "collapsed_row"    # 已折叠行菜单


class MenuAction(Enum):
    """菜单动作枚举"""
    # 基础操作
    COPY = "copy"
    PASTE = "paste"
    CUT = "cut"
    DELETE = "delete"
    SELECT_ALL = "select_all"
    CLEAR_SELECTION = "clear_selection"
    
    # 编辑操作
    EDIT_CELL = "edit_cell"
    BATCH_EDIT = "batch_edit"
    CANCEL_EDIT = "cancel_edit"
    
    # 展开操作
    EXPAND_ROW = "expand_row"
    COLLAPSE_ROW = "collapse_row"
    EXPAND_ALL = "expand_all"
    COLLAPSE_ALL = "collapse_all"
    
    # 批量操作
    BATCH_DELETE = "batch_delete"
    BATCH_COPY = "batch_copy"
    BATCH_EXPORT = "batch_export"
    BATCH_EXPAND = "batch_expand"
    BATCH_COLLAPSE = "batch_collapse"
    
    # 表格操作
    INSERT_ROW = "insert_row"
    SORT_COLUMN = "sort_column"
    HIDE_COLUMN = "hide_column"
    RESIZE_COLUMNS = "resize_columns"
    
    # 高级操作
    FILTER_ROWS = "filter_rows"
    REFRESH_DATA = "refresh_data"
    EXPORT_DATA = "export_data"


@dataclass
class MenuItemConfig:
    """菜单项配置数据"""
    action: MenuAction
    text: str
    icon: Optional[str] = None
    shortcut: Optional[str] = None
    enabled: bool = True
    visible: bool = True
    separator_after: bool = False
    tooltip: str = ""
    callback: Optional[Callable] = None


@dataclass
class ContextMenuContext:
    """右键菜单上下文数据"""
    menu_type: ContextMenuType
    clicked_row: int = -1
    clicked_column: int = -1
    selected_rows: List[int] = None
    selected_columns: List[int] = None
    click_position: QPoint = None
    is_editing: bool = False
    row_expanded: bool = False
    
    def __post_init__(self):
        if self.selected_rows is None:
            self.selected_rows = []
        if self.selected_columns is None:
            self.selected_columns = []


@dataclass
class TableRowData:
    """表格行数据结构"""
    id: str                           # 行唯一标识
    main_data: Dict[str, Any]        # 主行数据
    detail_data: Dict[str, Any]      # 详情数据
    expand_state: ExpandState = ExpandState.COLLAPSED
    expand_height: int = 0           # 展开高度
    animation_progress: float = 0.0  # 动画进度 (0.0-1.0)
    is_visible: bool = True          # 是否可见
    confidence_score: float = 1.0    # 数据置信度


class TableAnimationManager(QObject):
    """
    表格动画管理器
    
    负责管理表格展开/折叠的动画效果，实现60fps流畅动画。
    """
    
    # 动画完成信号
    animation_finished = pyqtSignal(str, ExpandState)  # row_id, final_state
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 动画配置
        self.animation_duration = 250  # 动画持续时间(ms)
        self.easing_curve = QEasingCurve.OutCubic
        
        # 活跃动画管理
        self.active_animations: Dict[str, QPropertyAnimation] = {}
        
        # 性能计数器
        self.animation_count = 0
        self.performance_stats = {
            'total_animations': 0,
            'average_duration': 0.0,
            'fps_target': 60.0
        }
        
        # 创建虚拟目标对象用于动画 (修复动画目标缺失警告)
        self.animation_target = QObject()
        self.animation_target.setProperty("animationValue", 0)
    
    def start_expand_animation(self, row_id: str, target_height: int, 
                             progress_callback=None) -> bool:
        """
        开始展开动画
        
        Args:
            row_id: 行标识
            target_height: 目标高度
            progress_callback: 进度回调函数
            
        Returns:
            bool: 动画启动是否成功
        """
        try:
            # 停止现有动画
            self.stop_animation(row_id)
            
            # 创建动画对象并设置目标
            animation = QPropertyAnimation(self.animation_target, b"animationValue")
            animation.setDuration(self.animation_duration)
            animation.setEasingCurve(self.easing_curve)
            animation.setStartValue(0)
            animation.setEndValue(target_height)
            
            # 连接回调
            if progress_callback:
                animation.valueChanged.connect(
                    lambda value: progress_callback(row_id, value / target_height if target_height > 0 else 0)
                )
            
            # 动画完成处理
            animation.finished.connect(
                lambda: self._on_animation_finished(row_id, ExpandState.EXPANDED)
            )
            
            # 存储并启动动画
            self.active_animations[row_id] = animation
            animation.start()
            
            self.performance_stats['total_animations'] += 1
            self.logger.debug(f"开始展开动画: {row_id}, 目标高度: {target_height}px")
            
            return True
            
        except Exception as e:
            self.logger.error(f"展开动画启动失败: {row_id}, 错误: {e}")
            return False
    
    def start_collapse_animation(self, row_id: str, current_height: int,
                               progress_callback=None) -> bool:
        """
        开始折叠动画
        
        Args:
            row_id: 行标识
            current_height: 当前高度
            progress_callback: 进度回调函数
            
        Returns:
            bool: 动画启动是否成功
        """
        try:
            # 停止现有动画
            self.stop_animation(row_id)
            
            # 创建动画对象并设置目标
            animation = QPropertyAnimation(self.animation_target, b"animationValue")
            animation.setDuration(self.animation_duration)
            animation.setEasingCurve(self.easing_curve)
            animation.setStartValue(current_height)
            animation.setEndValue(0)
            
            # 连接回调
            if progress_callback:
                animation.valueChanged.connect(
                    lambda value: progress_callback(row_id, 1.0 - (value / current_height if current_height > 0 else 0))
                )
            
            # 动画完成处理
            animation.finished.connect(
                lambda: self._on_animation_finished(row_id, ExpandState.COLLAPSED)
            )
            
            # 存储并启动动画
            self.active_animations[row_id] = animation
            animation.start()
            
            self.performance_stats['total_animations'] += 1
            self.logger.debug(f"开始折叠动画: {row_id}, 当前高度: {current_height}px")
            
            return True
            
        except Exception as e:
            self.logger.error(f"折叠动画启动失败: {row_id}, 错误: {e}")
            return False
    
    def stop_animation(self, row_id: str) -> bool:
        """
        停止指定行的动画
        
        Args:
            row_id: 行标识
            
        Returns:
            bool: 是否成功停止
        """
        if row_id in self.active_animations:
            animation = self.active_animations[row_id]
            animation.stop()
            del self.active_animations[row_id]
            return True
        return False
    
    def is_animating(self, row_id: str) -> bool:
        """检查指定行是否正在动画"""
        return row_id in self.active_animations
    
    def _on_animation_finished(self, row_id: str, final_state: ExpandState):
        """动画完成处理"""
        if row_id in self.active_animations:
            del self.active_animations[row_id]
        
        self.animation_finished.emit(row_id, final_state)
        self.logger.debug(f"动画完成: {row_id}, 最终状态: {final_state.value}")


class ExpandableTableModel(QAbstractTableModel):
    """
    可展开表格数据模型
    
    支持虚拟化数据管理和动态行高计算。
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 数据存储
        self.row_data: List[TableRowData] = []
        self.headers: List[str] = []
        
        # 虚拟化配置
        self.visible_rows: List[int] = []  # 可见行索引
        self.viewport_start = 0
        self.viewport_end = 0
        
        # 性能优化
        self.cache_size = 100
        self.data_cache: Dict[Tuple[int, int], Any] = {}
    
    def set_data(self, data: List[Dict[str, Any]], headers: List[str]):
        """
        设置表格数据
        
        Args:
            data: 行数据列表
            headers: 表头列表
        """
        self.beginResetModel()
        
        try:
            self.headers = headers.copy()
            self.row_data.clear()
            
            # 转换数据格式
            for i, row in enumerate(data):
                row_data = TableRowData(
                    id=f"row_{i}",
                    main_data=row.copy(),
                    detail_data=self._generate_detail_data(row),
                    confidence_score=row.get('confidence', 1.0)
                )
                self.row_data.append(row_data)
            
            # 初始化可见行
            self._update_visible_rows()
            
            self.logger.info(f"表格数据已设置: {len(self.row_data)} 行, {len(headers)} 列")
            
        except Exception as e:
            self.logger.error(f"设置表格数据失败: {e}")
        finally:
            self.endResetModel()
    
    def _generate_detail_data(self, main_data: Dict[str, Any]) -> Dict[str, Any]:
        """根据主数据生成详情数据"""
        detail = {
            '创建时间': '2025-06-19 16:00:00',
            '修改时间': '2025-06-19 16:15:30',
            '操作人': '系统管理员',
            '备注': '自动生成的详情数据',
            '数据来源': '工资异动检测系统',
            '审核状态': '待审核'
        }
        return detail
    
    def _update_visible_rows(self):
        """更新可见行索引"""
        self.visible_rows = list(range(len(self.row_data)))
    
    def rowCount(self, parent=QModelIndex()) -> int:
        """返回行数"""
        return len(self.row_data)
    
    def columnCount(self, parent=QModelIndex()) -> int:
        """返回列数"""
        return len(self.headers)
    
    def data(self, index: QModelIndex, role: int = Qt.DisplayRole) -> Any:
        """获取数据"""
        if not index.isValid():
            return QVariant()
        
        row, col = index.row(), index.column()
        
        if row >= len(self.row_data) or col >= len(self.headers):
            return QVariant()
        
        row_data = self.row_data[row]
        header = self.headers[col]
        
        if role == Qt.DisplayRole:
            return row_data.main_data.get(header, "")
        elif role == Qt.BackgroundRole:
            # 根据数据置信度设置背景色
            alpha = int(255 * row_data.confidence_score)
            return QColor(255, 255, 255, alpha)
        
        return QVariant()
    
    def headerData(self, section: int, orientation: Qt.Orientation, 
                   role: int = Qt.DisplayRole) -> Any:
        """获取表头数据"""
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal and section < len(self.headers):
                return self.headers[section]
            elif orientation == Qt.Vertical:
                return str(section + 1)
        
        return QVariant()
    
    def get_row_data(self, row: int) -> Optional[TableRowData]:
        """获取指定行的完整数据"""
        if 0 <= row < len(self.row_data):
            return self.row_data[row]
        return None
    
    def update_expand_state(self, row: int, state: ExpandState, 
                          height: int = 0, progress: float = 0.0):
        """更新行展开状态"""
        try:
            if 0 <= row < len(self.row_data):
                row_data = self.row_data[row]
                row_data.expand_state = state
                row_data.expand_height = height
                row_data.animation_progress = progress
                
                # 发射数据变化信号
                start_index = self.index(row, 0)
                end_index = self.index(row, self.columnCount() - 1)
                self.dataChanged.emit(start_index, end_index)
                
        except Exception as e:
            self.logger.error(f"更新展开状态失败: {e}")
    
    def move_row_data(self, from_row: int, to_row: int) -> bool:
        """移动行数据"""
        try:
            if (from_row < 0 or to_row < 0 or 
                from_row >= len(self.row_data) or 
                to_row >= len(self.row_data) or
                from_row == to_row):
                return False
            
            # 开始移动操作
            if to_row > from_row:
                self.beginMoveRows(QModelIndex(), from_row, from_row, QModelIndex(), to_row + 1)
            else:
                self.beginMoveRows(QModelIndex(), from_row, from_row, QModelIndex(), to_row)
            
            # 移动数据
            row_data = self.row_data.pop(from_row)
            actual_target = to_row if to_row < from_row else to_row - 1
            self.row_data.insert(actual_target, row_data)
            
            # 完成移动操作
            self.endMoveRows()
            
            self.logger.info(f"行数据移动成功: {from_row} -> {actual_target}")
            return True
            
        except Exception as e:
            self.logger.error(f"移动行数据失败: {e}")
            return False


class CellEditState(Enum):
    """单元格编辑状态枚举"""
    NORMAL = "normal"        # 正常显示状态
    EDITING = "editing"      # 编辑状态
    VALIDATING = "validating"  # 验证状态
    ERROR = "error"          # 错误状态


@dataclass
class CellEditData:
    """单元格编辑数据"""
    row: int
    column: int
    original_value: Any
    current_value: Any
    edit_state: CellEditState = CellEditState.NORMAL
    validator: Optional[QValidator] = None
    error_message: str = ""
    edit_widget: Optional[QWidget] = None


class TableCellEditor(QStyledItemDelegate):
    """
    表格单元格编辑器
    
    提供不同数据类型的编辑控件和验证逻辑
    """
    
    # 编辑完成信号
    edit_finished = pyqtSignal(int, int, object, bool)  # row, column, value, is_valid
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 编辑器类型映射
        self.editor_types = {
            'string': QLineEdit,
            'int': QSpinBox,
            'float': QDoubleSpinBox,
            'date': QDateEdit,
            'bool': QCheckBox,
            'text': QTextEdit,
            'choice': QComboBox
        }
        
        # 当前编辑状态
        self.current_edit: Optional[CellEditData] = None
    
    def createEditor(self, parent: QWidget, option, index: QModelIndex) -> QWidget:
        """创建编辑器控件"""
        try:
            # 获取单元格数据类型
            data_type = self._detect_data_type(index)
            
            # 创建对应的编辑器
            editor = self._create_typed_editor(data_type, parent)
            
            if editor:
                # 设置编辑器样式
                editor.setStyleSheet("""
                    QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox {
                        border: 2px solid #2196F3;
                        border-radius: 4px;
                        padding: 4px;
                        background-color: white;
                        font-size: 12px;
                    }
                    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, 
                    QDateEdit:focus, QComboBox:focus {
                        border-color: #1976D2;
                        /* 移除了 box-shadow 属性，PyQt5不支持 */
                    }
                """)
                
                # 绑定事件
                self._setup_editor_events(editor, index)
                
                self.logger.debug(f"创建编辑器: 行{index.row()}, 列{index.column()}, 类型{data_type}")
            
            return editor
            
        except Exception as e:
            self.logger.error(f"创建编辑器失败: {e}")
            return super().createEditor(parent, option, index)
    
    def _detect_data_type(self, index: QModelIndex) -> str:
        """检测数据类型"""
        try:
            data = index.data(Qt.DisplayRole)
            
            if isinstance(data, bool):
                return 'bool'
            elif isinstance(data, int):
                return 'int'
            elif isinstance(data, float):
                return 'float'
            elif isinstance(data, (QDate, QDateTime)):
                return 'date'
            elif isinstance(data, str):
                if len(data) > 100:
                    return 'text'
                else:
                    return 'string'
            else:
                return 'string'
                
        except Exception as e:
            self.logger.error(f"数据类型检测失败: {e}")
            return 'string'
    
    def _create_typed_editor(self, data_type: str, parent: QWidget) -> QWidget:
        """创建特定类型的编辑器"""
        try:
            if data_type == 'string':
                editor = QLineEdit(parent)
                editor.setMaxLength(255)
                return editor
                
            elif data_type == 'int':
                editor = QSpinBox(parent)
                editor.setRange(-999999, 999999)
                return editor
                
            elif data_type == 'float':
                editor = QDoubleSpinBox(parent)
                editor.setRange(-999999.99, 999999.99)
                editor.setDecimals(2)
                return editor
                
            elif data_type == 'date':
                editor = QDateEdit(parent)
                editor.setDate(QDate.currentDate())
                editor.setCalendarPopup(True)
                return editor
                
            elif data_type == 'bool':
                editor = QCheckBox(parent)
                return editor
                
            elif data_type == 'text':
                editor = QTextEdit(parent)
                editor.setMaximumHeight(80)
                return editor
                
            elif data_type == 'choice':
                editor = QComboBox(parent)
                editor.setEditable(True)
                return editor
                
            else:
                return QLineEdit(parent)
                
        except Exception as e:
            self.logger.error(f"创建编辑器失败: {data_type}, {e}")
            return QLineEdit(parent)
    
    def _setup_editor_events(self, editor: QWidget, index: QModelIndex):
        """设置编辑器事件"""
        try:
            # 通用键盘事件
            if hasattr(editor, 'keyPressEvent'):
                original_key_event = editor.keyPressEvent
                
                def enhanced_key_event(event: QKeyEvent):
                    if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                        self._commit_edit(editor, index, True)
                    elif event.key() == Qt.Key_Escape:
                        self._commit_edit(editor, index, False)
                    else:
                        original_key_event(event)
                
                editor.keyPressEvent = enhanced_key_event
            
            # 特定类型的事件
            if isinstance(editor, QLineEdit):
                editor.editingFinished.connect(
                    lambda: self._commit_edit(editor, index, True)
                )
            elif isinstance(editor, (QSpinBox, QDoubleSpinBox)):
                editor.editingFinished.connect(
                    lambda: self._commit_edit(editor, index, True)
                )
            elif isinstance(editor, QComboBox):
                editor.currentTextChanged.connect(
                    lambda: self._validate_edit(editor, index)
                )
            elif isinstance(editor, QCheckBox):
                editor.stateChanged.connect(
                    lambda: self._commit_edit(editor, index, True)
                )
            
        except Exception as e:
            self.logger.error(f"设置编辑器事件失败: {e}")
    
    def _validate_edit(self, editor: QWidget, index: QModelIndex) -> bool:
        """验证编辑内容"""
        try:
            # 获取编辑后的值
            value = self._get_editor_value(editor)
            
            # 基础验证
            if value is None:
                return False
            
            # 数据类型验证
            data_type = self._detect_data_type(index)
            if not self._validate_data_type(value, data_type):
                return False
            
            # 自定义验证规则
            if not self._custom_validation(value, index):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"编辑验证失败: {e}")
            return False
    
    def _commit_edit(self, editor: QWidget, index: QModelIndex, save: bool):
        """提交或取消编辑"""
        try:
            if save:
                # 验证并保存
                if self._validate_edit(editor, index):
                    value = self._get_editor_value(editor)
                    self.edit_finished.emit(index.row(), index.column(), value, True)
                else:
                    self.edit_finished.emit(index.row(), index.column(), None, False)
            else:
                # 取消编辑
                self.edit_finished.emit(index.row(), index.column(), None, False)
            
        except Exception as e:
            self.logger.error(f"提交编辑失败: {e}")
    
    def _get_editor_value(self, editor: QWidget) -> Any:
        """获取编辑器的值"""
        try:
            if isinstance(editor, QLineEdit):
                return editor.text()
            elif isinstance(editor, QSpinBox):
                return editor.value()
            elif isinstance(editor, QDoubleSpinBox):
                return editor.value()
            elif isinstance(editor, QDateEdit):
                return editor.date().toPyDate()
            elif isinstance(editor, QCheckBox):
                return editor.isChecked()
            elif isinstance(editor, QTextEdit):
                return editor.toPlainText()
            elif isinstance(editor, QComboBox):
                return editor.currentText()
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"获取编辑器值失败: {e}")
            return None
    
    def _validate_data_type(self, value: Any, data_type: str) -> bool:
        """验证数据类型"""
        try:
            if data_type == 'int':
                int(value)
            elif data_type == 'float':
                float(value)
            elif data_type == 'string':
                str(value)
            return True
        except ValueError:
            return False
    
    def _custom_validation(self, value: Any, index: QModelIndex) -> bool:
        """自定义验证规则"""
        # 可以在这里添加业务逻辑验证
        return True


class TableContextMenu(QMenu):
    """
    表格右键菜单系统
    
    提供上下文感知的智能菜单，根据点击位置、选择状态、编辑状态等
    动态生成合适的菜单项。
    """
    
    # 菜单动作信号
    action_triggered = pyqtSignal(str, object)  # action_name, context_data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 菜单配置
        self.menu_configs = self._create_menu_configs()
        
        # 性能统计
        self.menu_stats = {
            'total_shows': 0,
            'action_counts': {},
            'average_show_time': 0.0
        }
        
    def _create_menu_configs(self) -> Dict[ContextMenuType, List[MenuItemConfig]]:
        """创建菜单配置"""
        configs = {
            # 单行菜单
            ContextMenuType.SINGLE_ROW: [
                MenuItemConfig(MenuAction.EDIT_CELL, "编辑单元格", "edit", "F2"),
                MenuItemConfig(MenuAction.COPY, "复制", "copy", "Ctrl+C"),
                MenuItemConfig(MenuAction.CUT, "剪切", "cut", "Ctrl+X"),
                MenuItemConfig(MenuAction.DELETE, "删除行", "delete", "Delete", separator_after=True),
                MenuItemConfig(MenuAction.EXPAND_ROW, "展开行", "expand", "+"),
                MenuItemConfig(MenuAction.COLLAPSE_ROW, "折叠行", "collapse", "-", separator_after=True),
                MenuItemConfig(MenuAction.INSERT_ROW, "插入新行", "add", "Ctrl+I"),
                MenuItemConfig(MenuAction.EXPORT_DATA, "导出数据", "export", "Ctrl+E"),
            ],
            
            # 多行菜单
            ContextMenuType.MULTI_ROW: [
                MenuItemConfig(MenuAction.BATCH_EDIT, "批量编辑", "batch_edit", "Ctrl+B"),
                MenuItemConfig(MenuAction.BATCH_COPY, "批量复制", "copy", "Ctrl+C"),
                MenuItemConfig(MenuAction.BATCH_DELETE, "批量删除", "delete", "Delete", separator_after=True),
                MenuItemConfig(MenuAction.BATCH_EXPAND, "批量展开", "expand_all", "Ctrl++"),
                MenuItemConfig(MenuAction.BATCH_COLLAPSE, "批量折叠", "collapse_all", "Ctrl+-", separator_after=True),
                MenuItemConfig(MenuAction.BATCH_EXPORT, "批量导出", "export", "Ctrl+E"),
                MenuItemConfig(MenuAction.CLEAR_SELECTION, "清除选择", "clear", "Esc"),
            ],
            
            # 单元格编辑菜单
            ContextMenuType.CELL_EDIT: [
                MenuItemConfig(MenuAction.COPY, "复制", "copy", "Ctrl+C"),
                MenuItemConfig(MenuAction.PASTE, "粘贴", "paste", "Ctrl+V"),
                MenuItemConfig(MenuAction.CUT, "剪切", "cut", "Ctrl+X", separator_after=True),
                MenuItemConfig(MenuAction.CANCEL_EDIT, "取消编辑", "cancel", "Esc"),
            ],
            
            # 表头菜单
            ContextMenuType.HEADER: [
                MenuItemConfig(MenuAction.SORT_COLUMN, "排序列", "sort", ""),
                MenuItemConfig(MenuAction.HIDE_COLUMN, "隐藏列", "hide", ""),
                MenuItemConfig(MenuAction.RESIZE_COLUMNS, "调整列宽", "resize", "", separator_after=True),
                MenuItemConfig(MenuAction.SELECT_ALL, "全选", "select_all", "Ctrl+A"),
                MenuItemConfig(MenuAction.EXPORT_DATA, "导出数据", "export", "Ctrl+E"),
            ],
            
            # 空白区域菜单
            ContextMenuType.EMPTY_AREA: [
                MenuItemConfig(MenuAction.SELECT_ALL, "全选", "select_all", "Ctrl+A"),
                MenuItemConfig(MenuAction.CLEAR_SELECTION, "清除选择", "clear", "Esc", separator_after=True),
                MenuItemConfig(MenuAction.EXPAND_ALL, "展开所有", "expand_all", "Ctrl+Shift++"),
                MenuItemConfig(MenuAction.COLLAPSE_ALL, "折叠所有", "collapse_all", "Ctrl+Shift+-", separator_after=True),
                MenuItemConfig(MenuAction.INSERT_ROW, "插入新行", "add", "Ctrl+I"),
                MenuItemConfig(MenuAction.REFRESH_DATA, "刷新数据", "refresh", "F5"),
                MenuItemConfig(MenuAction.EXPORT_DATA, "导出数据", "export", "Ctrl+E"),
            ],
        }
        
        return configs
    
    def show_context_menu(self, context: ContextMenuContext, position: QPoint):
        """显示上下文菜单"""
        try:
            start_time = time.time()
            
            # 清空当前菜单
            self.clear()
            
            # 根据上下文构建菜单
            menu_items = self._build_menu_items(context)
            
            if not menu_items:
                self.logger.debug("没有可用的菜单项")
                return
            
            # 添加菜单项到菜单
            self._populate_menu(menu_items, context)
            
            # 显示菜单
            if not self.isEmpty():
                self.exec_(position)
                
                # 统计性能
                show_time = (time.time() - start_time) * 1000
                self.menu_stats['total_shows'] += 1
                self.menu_stats['average_show_time'] = (
                    (self.menu_stats['average_show_time'] * (self.menu_stats['total_shows'] - 1) + show_time) /
                    self.menu_stats['total_shows']
                )
                
                self.logger.debug(f"显示右键菜单: {context.menu_type.value}, 用时: {show_time:.1f}ms")
            
        except Exception as e:
            self.logger.error(f"显示右键菜单失败: {e}")
    
    def _build_menu_items(self, context: ContextMenuContext) -> List[MenuItemConfig]:
        """根据上下文构建菜单项"""
        try:
            # 获取基础菜单配置
            base_configs = self.menu_configs.get(context.menu_type, [])
            
            # 复制配置以便修改
            menu_items = []
            
            for config in base_configs:
                # 创建配置副本
                item_config = MenuItemConfig(
                    action=config.action,
                    text=config.text,
                    icon=config.icon,
                    shortcut=config.shortcut,
                    enabled=config.enabled,
                    visible=config.visible,
                    separator_after=config.separator_after,
                    tooltip=config.tooltip,
                    callback=config.callback
                )
                
                # 根据上下文调整菜单项
                self._adjust_menu_item(item_config, context)
                
                if item_config.visible:
                    menu_items.append(item_config)
            
            return menu_items
            
        except Exception as e:
            self.logger.error(f"构建菜单项失败: {e}")
            return []
    
    def _adjust_menu_item(self, item_config: MenuItemConfig, context: ContextMenuContext):
        """根据上下文调整菜单项"""
        try:
            action = item_config.action
            
            # 展开/折叠状态相关调整
            if action == MenuAction.EXPAND_ROW:
                item_config.visible = not context.row_expanded
            elif action == MenuAction.COLLAPSE_ROW:
                item_config.visible = context.row_expanded
            
            # 编辑状态相关调整
            elif action == MenuAction.EDIT_CELL:
                item_config.enabled = not context.is_editing and context.clicked_column > 0
            elif action == MenuAction.CANCEL_EDIT:
                item_config.visible = context.is_editing
            
            # 多选状态相关调整
            elif action in [MenuAction.BATCH_EDIT, MenuAction.BATCH_DELETE, 
                          MenuAction.BATCH_COPY, MenuAction.BATCH_EXPORT,
                          MenuAction.BATCH_EXPAND, MenuAction.BATCH_COLLAPSE]:
                item_config.enabled = len(context.selected_rows) > 1
            
            # 选择状态相关调整
            elif action == MenuAction.CLEAR_SELECTION:
                item_config.enabled = len(context.selected_rows) > 0
            
            # 动态文本调整
            if action == MenuAction.BATCH_DELETE and len(context.selected_rows) > 1:
                item_config.text = f"删除 {len(context.selected_rows)} 行"
            elif action == MenuAction.BATCH_COPY and len(context.selected_rows) > 1:
                item_config.text = f"复制 {len(context.selected_rows)} 行"
            elif action == MenuAction.BATCH_EXPORT and len(context.selected_rows) > 1:
                item_config.text = f"导出 {len(context.selected_rows)} 行"
            
        except Exception as e:
            self.logger.error(f"调整菜单项失败: {action}, 错误: {e}")
    
    def _populate_menu(self, menu_items: List[MenuItemConfig], context: ContextMenuContext):
        """填充菜单内容"""
        try:
            for item_config in menu_items:
                if not item_config.visible:
                    continue
                
                # 创建动作
                action = QAction(item_config.text, self)
                action.setEnabled(item_config.enabled)
                
                # 设置快捷键
                if item_config.shortcut:
                    action.setShortcut(item_config.shortcut)
                
                # 设置工具提示
                if item_config.tooltip:
                    action.setToolTip(item_config.tooltip)
                
                # 连接信号
                action.triggered.connect(
                    lambda checked, act=item_config.action: self._on_action_triggered(act, context)
                )
                
                # 添加到菜单
                self.addAction(action)
                
                # 添加分隔符
                if item_config.separator_after:
                    self.addSeparator()
            
        except Exception as e:
            self.logger.error(f"填充菜单失败: {e}")
    
    def _on_action_triggered(self, action: MenuAction, context: ContextMenuContext):
        """处理菜单动作触发"""
        try:
            # 统计动作使用
            action_name = action.value
            if action_name not in self.menu_stats['action_counts']:
                self.menu_stats['action_counts'][action_name] = 0
            self.menu_stats['action_counts'][action_name] += 1
            
            # 发出信号
            self.action_triggered.emit(action_name, context)
            
            self.logger.debug(f"菜单动作触发: {action_name}")
            
        except Exception as e:
            self.logger.error(f"处理菜单动作失败: {action}, 错误: {e}")
    
    def get_menu_stats(self) -> Dict[str, Any]:
        """获取菜单统计信息"""
        return {
            'total_shows': self.menu_stats['total_shows'],
            'action_counts': self.menu_stats['action_counts'].copy(),
            'average_show_time': self.menu_stats['average_show_time'],
            'most_used_action': max(
                self.menu_stats['action_counts'].items(),
                key=lambda x: x[1],
                default=("none", 0)
            )[0] if self.menu_stats['action_counts'] else "none"
        }


class KeyboardShortcutManager(QObject):
    """
    键盘快捷键管理器
    
    管理表格组件的所有快捷键，提供：
    - 快捷键注册和管理
    - 快捷键冲突检测
    - 快捷键帮助系统
    - 快捷键统计和分析
    """
    
    # 快捷键触发信号
    shortcut_triggered = pyqtSignal(str, object)  # shortcut_name, context_data
    help_requested = pyqtSignal(list)             # shortcuts_list
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 快捷键映射
        self.shortcuts = {}  # QShortcut对象映射
        self.shortcut_configs = self._create_shortcut_configs()
        
        # 快捷键统计
        self.usage_stats = {}
        self.conflict_log = []
        
        # 初始化快捷键
        self._register_shortcuts()
    
    def _create_shortcut_configs(self) -> Dict[str, Dict[str, Any]]:
        """创建快捷键配置"""
        configs = {
            # 基础编辑快捷键
            'edit_cell': {
                'key': 'F2',
                'description': '编辑当前单元格',
                'category': '编辑',
                'action': 'edit_cell',
                'enabled': True
            },
            'save_edit': {
                'key': 'Ctrl+Return',
                'description': '保存编辑并移到下一行',
                'category': '编辑',
                'action': 'save_edit',
                'enabled': True
            },
            'cancel_edit': {
                'key': 'Escape',
                'description': '取消当前编辑',
                'category': '编辑',
                'action': 'cancel_edit',
                'enabled': True
            },
            
            # 选择操作快捷键
            'select_all': {
                'key': 'Ctrl+A',
                'description': '全选所有行',
                'category': '选择',
                'action': 'select_all',
                'enabled': True
            },
            'clear_selection': {
                'key': 'Ctrl+D',
                'description': '清除所有选择',
                'category': '选择',
                'action': 'clear_selection',
                'enabled': True
            },
            'invert_selection': {
                'key': 'Ctrl+I',
                'description': '反选当前选择',
                'category': '选择',
                'action': 'invert_selection',
                'enabled': True
            },
            
            # 复制粘贴快捷键
            'copy_data': {
                'key': 'Ctrl+C',
                'description': '复制选中的数据',
                'category': '剪贴板',
                'action': 'copy_data',
                'enabled': True
            },
            'paste_data': {
                'key': 'Ctrl+V',
                'description': '粘贴数据',
                'category': '剪贴板',
                'action': 'paste_data',
                'enabled': True
            },
            'cut_data': {
                'key': 'Ctrl+X',
                'description': '剪切选中的数据',
                'category': '剪贴板',
                'action': 'cut_data',
                'enabled': True
            },
            
            # 展开折叠快捷键
            'expand_all': {
                'key': 'Ctrl+Plus',
                'description': '展开所有行',
                'category': '展开',
                'action': 'expand_all',
                'enabled': True
            },
            'collapse_all': {
                'key': 'Ctrl+Minus',
                'description': '折叠所有行',
                'category': '展开',
                'action': 'collapse_all',
                'enabled': True
            },
            'toggle_current_row': {
                'key': 'Space',
                'description': '切换当前行展开状态',
                'category': '展开',
                'action': 'toggle_current_row',
                'enabled': True
            },
            
            # 批量操作快捷键
            'batch_delete': {
                'key': 'Delete',
                'description': '删除选中的行',
                'category': '批量操作',
                'action': 'batch_delete',
                'enabled': True
            },
            'batch_export': {
                'key': 'Ctrl+E',
                'description': '导出选中的数据',
                'category': '批量操作',
                'action': 'batch_export',
                'enabled': True
            },
            
            # 导航快捷键
            'goto_first_row': {
                'key': 'Ctrl+Home',
                'description': '跳转到第一行',
                'category': '导航',
                'action': 'goto_first_row',
                'enabled': True
            },
            'goto_last_row': {
                'key': 'Ctrl+End',
                'description': '跳转到最后一行',
                'category': '导航',
                'action': 'goto_last_row',
                'enabled': True
            },
            
            # 系统快捷键
            'refresh_data': {
                'key': 'F5',
                'description': '刷新表格数据',
                'category': '系统',
                'action': 'refresh_data',
                'enabled': True
            },
            'show_help': {
                'key': 'F1',
                'description': '显示快捷键帮助',
                'category': '系统',
                'action': 'show_help',
                'enabled': True
            }
        }
        
        return configs
    
    def _register_shortcuts(self):
        """注册所有快捷键"""
        try:
            parent_widget = self.parent()
            if not parent_widget:
                self.logger.error("快捷键管理器没有父组件，无法注册快捷键")
                return
            
            for name, config in self.shortcut_configs.items():
                if config['enabled']:
                    try:
                        # 创建快捷键对象
                        shortcut = QShortcut(config['key'], parent_widget)
                        shortcut.activated.connect(
                            lambda checked=False, n=name: self._on_shortcut_activated(n)
                        )
                        
                        # 存储快捷键对象
                        self.shortcuts[name] = shortcut
                        
                        # 初始化统计
                        self.usage_stats[name] = 0
                        
                    except Exception as e:
                        self.logger.error(f"注册快捷键失败: {name} ({config['key']}), 错误: {e}")
                        self.conflict_log.append({
                            'shortcut': name,
                            'key': config['key'],
                            'error': str(e),
                            'timestamp': time.time()
                        })
            
            self.logger.info(f"快捷键注册完成: {len(self.shortcuts)}/{len(self.shortcut_configs)} 个")
            
        except Exception as e:
            self.logger.error(f"批量注册快捷键失败: {e}")
    
    def _on_shortcut_activated(self, shortcut_name: str):
        """处理快捷键激活"""
        try:
            # 更新使用统计
            if shortcut_name in self.usage_stats:
                self.usage_stats[shortcut_name] += 1
            
            # 获取快捷键配置
            config = self.shortcut_configs.get(shortcut_name)
            if not config:
                self.logger.error(f"未找到快捷键配置: {shortcut_name}")
                return
            
            # 创建上下文数据
            context_data = {
                'shortcut_name': shortcut_name,
                'key_sequence': config['key'],
                'description': config['description'],
                'category': config['category'],
                'action': config['action'],
                'timestamp': time.time()
            }
            
            # 发出信号
            self.shortcut_triggered.emit(shortcut_name, context_data)
            
            self.logger.debug(f"快捷键激活: {shortcut_name} ({config['key']})")
            
        except Exception as e:
            self.logger.error(f"处理快捷键激活失败: {shortcut_name}, 错误: {e}")
    
    def get_shortcuts_by_category(self) -> Dict[str, List[Dict[str, Any]]]:
        """按分类获取快捷键列表"""
        try:
            categories = {}
            
            for name, config in self.shortcut_configs.items():
                category = config['category']
                if category not in categories:
                    categories[category] = []
                
                shortcut_info = {
                    'name': name,
                    'key': config['key'],
                    'description': config['description'],
                    'enabled': config['enabled'],
                    'usage_count': self.usage_stats.get(name, 0)
                }
                categories[category].append(shortcut_info)
            
            # 按使用频率排序
            for category in categories:
                categories[category].sort(key=lambda x: x['usage_count'], reverse=True)
            
            return categories
            
        except Exception as e:
            self.logger.error(f"获取快捷键分类失败: {e}")
            return {}
    
    def get_shortcut_help_text(self) -> str:
        """获取快捷键帮助文本"""
        try:
            categories = self.get_shortcuts_by_category()
            help_lines = ["快捷键帮助\n" + "="*50]
            
            for category, shortcuts in categories.items():
                help_lines.append(f"\n【{category}】")
                for shortcut in shortcuts:
                    if shortcut['enabled']:
                        key = shortcut['key'].ljust(15)
                        desc = shortcut['description']
                        usage = f"(使用{shortcut['usage_count']}次)" if shortcut['usage_count'] > 0 else ""
                        help_lines.append(f"  {key} - {desc} {usage}")
            
            help_lines.append(f"\n总计: {len([s for cat in categories.values() for s in cat if s['enabled']])} 个可用快捷键")
            
            return "\n".join(help_lines)
            
        except Exception as e:
            self.logger.error(f"生成快捷键帮助文本失败: {e}")
            return "快捷键帮助生成失败"
    
    def enable_shortcut(self, shortcut_name: str, enabled: bool = True):
        """启用或禁用指定快捷键"""
        try:
            if shortcut_name in self.shortcut_configs:
                self.shortcut_configs[shortcut_name]['enabled'] = enabled
                
                if shortcut_name in self.shortcuts:
                    shortcut = self.shortcuts[shortcut_name]
                    shortcut.setEnabled(enabled)
                
                self.logger.debug(f"快捷键状态更新: {shortcut_name} = {'启用' if enabled else '禁用'}")
                return True
            else:
                self.logger.error(f"未找到快捷键: {shortcut_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"设置快捷键状态失败: {shortcut_name}, 错误: {e}")
            return False
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """获取快捷键使用统计"""
        try:
            total_usage = sum(self.usage_stats.values())
            most_used = max(self.usage_stats.items(), key=lambda x: x[1]) if self.usage_stats else ("none", 0)
            
            return {
                'total_shortcuts': len(self.shortcut_configs),
                'enabled_shortcuts': len([c for c in self.shortcut_configs.values() if c['enabled']]),
                'registered_shortcuts': len(self.shortcuts),
                'total_usage_count': total_usage,
                'most_used_shortcut': most_used[0],
                'most_used_count': most_used[1],
                'usage_stats': self.usage_stats.copy(),
                'conflict_count': len(self.conflict_log),
                'average_usage': total_usage / len(self.usage_stats) if self.usage_stats else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"获取快捷键统计失败: {e}")
            return {}
    
    def detect_conflicts(self) -> List[Dict[str, Any]]:
        """检测快捷键冲突"""
        try:
            conflicts = []
            key_map = {}
            
            # 建立按键映射
            for name, config in self.shortcut_configs.items():
                if config['enabled']:
                    key = config['key']
                    if key in key_map:
                        conflicts.append({
                            'key': key,
                            'shortcuts': [key_map[key], name],
                            'descriptions': [
                                self.shortcut_configs[key_map[key]]['description'],
                                config['description']
                            ]
                        })
                    else:
                        key_map[key] = name
            
            return conflicts
            
        except Exception as e:
            self.logger.error(f"检测快捷键冲突失败: {e}")
            return []


class DragSortManager(QObject):
    """
    拖拽排序管理器
    
    负责管理表格行的拖拽排序功能，提供：
    - 拖拽检测和状态管理
    - 拖拽预览和视觉反馈
    - 行数据重排序算法
    - 拖拽约束条件验证
    - 拖拽性能统计
    """
    
    # 拖拽事件信号
    drag_started = pyqtSignal(int)              # 拖拽开始信号 (source_row)
    drag_moved = pyqtSignal(int, int)           # 拖拽移动信号 (source_row, target_row)
    drag_completed = pyqtSignal(int, int, bool) # 拖拽完成信号 (from_row, to_row, success)
    drag_cancelled = pyqtSignal(int)            # 拖拽取消信号 (source_row)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 拖拽状态
        self.is_dragging = False
        self.drag_source_row = -1
        self.drag_target_row = -1
        self.drag_start_position = QPoint()
        self.drag_current_position = QPoint()
        
        # 拖拽配置
        self.drag_enabled = True
        self.drag_threshold = 10  # 拖拽开始阈值(像素)
        self.drag_preview_enabled = True
        
        # 拖拽约束
        self.cross_group_drag = True      # 允许跨组拖拽
        self.expanded_row_drag = True     # 允许展开行拖拽
        self.allowed_drag_rows = None     # 允许拖拽的行列表，None表示全部允许
        
        # 拖拽数据
        self.drag_data = None
        self.drag_preview_widget = None
        
        # 性能统计
        self.drag_statistics = {
            'total_drags': 0,
            'successful_drags': 0,
            'cancelled_drags': 0,
            'average_drag_time': 0.0,
            'longest_drag_distance': 0
        }
        
        self.logger.info("拖拽排序管理器初始化完成")
    
    def enable_drag_sort(self, enabled: bool):
        """启用/禁用拖拽排序功能"""
        try:
            self.drag_enabled = enabled
            
            if not enabled and self.is_dragging:
                # 如果正在拖拽，取消当前拖拽
                self.cancel_drag()
            
            self.logger.debug(f"拖拽排序功能{'启用' if enabled else '禁用'}")
            
        except Exception as e:
            self.logger.error(f"设置拖拽排序状态失败: {e}")
    
    def set_drag_constraints(self, 
                           cross_group: bool = True,
                           expanded_rows: bool = True,
                           allowed_rows: Optional[List[int]] = None):
        """设置拖拽约束条件"""
        try:
            self.cross_group_drag = cross_group
            self.expanded_row_drag = expanded_rows
            self.allowed_drag_rows = allowed_rows
            
            self.logger.debug(f"拖拽约束更新: 跨组={cross_group}, 展开行={expanded_rows}, 限制行数={len(allowed_rows) if allowed_rows else '无'}")
            
        except Exception as e:
            self.logger.error(f"设置拖拽约束失败: {e}")
    
    def start_drag(self, source_row: int, start_position: QPoint, row_data: Dict[str, Any]) -> bool:
        """开始拖拽操作"""
        try:
            if not self.drag_enabled:
                self.logger.debug("拖拽功能已禁用")
                return False
            
            # 检查拖拽约束
            if not self._validate_drag_source(source_row):
                self.logger.debug(f"行 {source_row} 不允许拖拽")
                return False
            
            # 记录拖拽开始时间
            self.drag_start_time = time.time()
            
            # 设置拖拽状态
            self.is_dragging = True
            self.drag_source_row = source_row
            self.drag_target_row = source_row
            self.drag_start_position = start_position
            self.drag_current_position = start_position
            self.drag_data = row_data.copy()
            
            # 更新统计
            self.drag_statistics['total_drags'] += 1
            
            # 发出拖拽开始信号
            self.drag_started.emit(source_row)
            
            self.logger.debug(f"拖拽开始: 源行 {source_row}")
            return True
            
        except Exception as e:
            self.logger.error(f"开始拖拽失败: 源行 {source_row}, 错误: {e}")
            return False
    
    def update_drag(self, target_row: int) -> bool:
        """更新拖拽状态"""
        try:
            if not self.is_dragging:
                return False
            
            # 检查目标行有效性
            if not self._validate_drag_target(target_row):
                return False
            
            # 更新拖拽状态
            old_target = self.drag_target_row
            self.drag_target_row = target_row
            
            # 发出拖拽移动信号
            if old_target != target_row:
                self.drag_moved.emit(self.drag_source_row, target_row)
                self.logger.debug(f"拖拽移动: {self.drag_source_row} -> {target_row}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新拖拽状态失败: 目标行 {target_row}, 错误: {e}")
            return False
    
    def complete_drag(self) -> bool:
        """完成拖拽操作"""
        try:
            if not self.is_dragging:
                return False
            
            source_row = self.drag_source_row
            target_row = self.drag_target_row
            
            # 检查是否真的发生了移动
            if source_row == target_row:
                self.cancel_drag()
                return False
            
            # 记录拖拽时间
            drag_time = time.time() - self.drag_start_time
            
            # 计算拖拽距离
            distance = (self.drag_current_position - self.drag_start_position).manhattanLength()
            
            # 更新统计
            self.drag_statistics['successful_drags'] += 1
            self.drag_statistics['average_drag_time'] = (
                (self.drag_statistics['average_drag_time'] * (self.drag_statistics['successful_drags'] - 1) + drag_time) /
                self.drag_statistics['successful_drags']
            )
            if distance > self.drag_statistics['longest_drag_distance']:
                self.drag_statistics['longest_drag_distance'] = distance
            
            # 清理拖拽状态
            self._reset_drag_state()
            
            # 发出拖拽完成信号
            self.drag_completed.emit(source_row, target_row, True)
            
            self.logger.info(f"拖拽完成: {source_row} -> {target_row}, 耗时: {drag_time:.3f}s")
            return True
            
        except Exception as e:
            self.logger.error(f"完成拖拽失败: {e}")
            self.cancel_drag()
            return False
    
    def cancel_drag(self):
        """取消拖拽操作"""
        try:
            if self.is_dragging:
                source_row = self.drag_source_row
                
                # 更新统计
                self.drag_statistics['cancelled_drags'] += 1
                
                # 清理拖拽状态
                self._reset_drag_state()
                
                # 发出拖拽取消信号
                self.drag_cancelled.emit(source_row)
                
                self.logger.debug(f"拖拽已取消: 源行 {source_row}")
                
        except Exception as e:
            self.logger.error(f"取消拖拽失败: {e}")
    
    def _validate_drag_source(self, row: int) -> bool:
        """验证拖拽源行"""
        try:
            # 检查行索引有效性
            if row < 0:
                return False
            
            # 检查是否在允许拖拽的行列表中
            if self.allowed_drag_rows is not None:
                if row not in self.allowed_drag_rows:
                    return False
            
            # 这里可以添加更多约束检查
            # 比如检查行是否展开、是否属于某个组等
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证拖拽源行失败: 行 {row}, 错误: {e}")
            return False
    
    def _validate_drag_target(self, row: int) -> bool:
        """验证拖拽目标行"""
        try:
            # 检查行索引有效性
            if row < 0:
                return False
            
            # 不能拖拽到自己
            if row == self.drag_source_row:
                return True  # 允许，但不会执行移动
            
            # 这里可以添加更多约束检查
            # 比如检查跨组拖拽、展开行约束等
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证拖拽目标行失败: 行 {row}, 错误: {e}")
            return False
    
    def _reset_drag_state(self):
        """重置拖拽状态"""
        self.is_dragging = False
        self.drag_source_row = -1
        self.drag_target_row = -1
        self.drag_start_position = QPoint()
        self.drag_current_position = QPoint()
        self.drag_data = None
        self.drag_preview_widget = None
    
    def create_drag_preview(self, parent_widget, row: int) -> QPixmap:
        """创建拖拽预览图"""
        try:
            if not hasattr(parent_widget, 'rowHeight') or not hasattr(parent_widget, 'columnCount'):
                # 创建简单的预览图
                pixmap = QPixmap(200, 30)
                pixmap.fill(QColor(100, 100, 100, 100))
                return pixmap
            
            # 获取行高和列数
            row_height = parent_widget.rowHeight(row)
            col_count = parent_widget.columnCount()
            
            # 计算预览图大小
            preview_width = min(400, parent_widget.width())
            preview_height = row_height
            
            # 创建预览图
            pixmap = QPixmap(preview_width, preview_height)
            pixmap.fill(QColor(255, 255, 255, 200))
            
            # 绘制预览内容
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # 绘制边框
            painter.setPen(QPen(QColor(33, 150, 243), 2))
            painter.drawRect(pixmap.rect())
            
            # 绘制文本
            painter.setPen(QColor(0, 0, 0))
            painter.drawText(10, 20, f"拖拽行 {row}")
            
            painter.end()
            
            return pixmap
            
        except Exception as e:
            self.logger.error(f"创建拖拽预览图失败: 行 {row}, 错误: {e}")
            # 返回默认预览图
            pixmap = QPixmap(200, 30)
            pixmap.fill(QColor(100, 100, 100, 100))
            return pixmap
    
    def get_drag_statistics(self) -> Dict[str, Any]:
        """获取拖拽统计信息"""
        return {
            'enabled': self.drag_enabled,
            'currently_dragging': self.is_dragging,
            'drag_threshold': self.drag_threshold,
            'constraints': {
                'cross_group_drag': self.cross_group_drag,
                'expanded_row_drag': self.expanded_row_drag,
                'allowed_rows_count': len(self.allowed_drag_rows) if self.allowed_drag_rows else None
            },
            'statistics': self.drag_statistics.copy(),
            'success_rate': (
                self.drag_statistics['successful_drags'] / self.drag_statistics['total_drags'] * 100
                if self.drag_statistics['total_drags'] > 0 else 0.0
            )
        }


class VirtualizedExpandableTable(QTableWidget):
    """
    虚拟化可展开表格主组件
    
    基于虚拟化算法实现的高性能可展开表格，支持：
    - 大数据量渲染优化 (1000+行)
    - 流畅的展开/折叠动画 (60fps目标)
    - 智能内存管理和缓存策略
    - Material Design视觉效果
    - 表格内编辑：双击编辑、Enter确认、Esc取消 (P4-001)
    - 多选批量操作：Ctrl+Click、Shift+Click、8种批量功能 (P4-002)
    - 右键菜单系统：上下文感知菜单、动态菜单项 (P4-003)
    - 快捷键支持系统：10+快捷键、帮助系统、冲突检测 (P4-004)
    """
    
    # 信号定义
    row_expanded = pyqtSignal(int, dict)    # 行展开信号
    row_collapsed = pyqtSignal(int, dict)   # 行折叠信号
    selection_changed = pyqtSignal(list)    # 选择变化信号
    cell_edited = pyqtSignal(int, int, object, object)  # 单元格编辑信号 (row, column, old_value, new_value)
    edit_mode_changed = pyqtSignal(bool)    # 编辑模式变化信号
    batch_operation_completed = pyqtSignal(str, int, list)  # 批量操作完成信号 (operation, count, affected_rows)
    context_menu_requested = pyqtSignal(object, object)  # 右键菜单请求信号 (context, position)
    context_action_triggered = pyqtSignal(str, object)   # 右键菜单动作触发信号 (action_name, context)
    shortcut_triggered = pyqtSignal(str, object)         # 快捷键触发信号 (shortcut_name, context)
    shortcut_help_requested = pyqtSignal()               # 快捷键帮助请求信号
    # P4-005 拖拽排序信号
    row_drag_started = pyqtSignal(int)                   # 行拖拽开始信号 (source_row)
    row_drag_moved = pyqtSignal(int, int)                # 行拖拽移动信号 (source_row, target_row)
    row_reordered = pyqtSignal(int, int)                 # 行重排序完成信号 (from_row, to_row)
    drag_operation_completed = pyqtSignal(str, bool)     # 拖拽操作完成信号 (operation_type, success)
    # 字段映射编辑信号
    header_edited = pyqtSignal(int, str, str)           # 表头编辑信号 (column, old_name, new_name)
    field_mapping_updated = pyqtSignal(str, dict)       # 字段映射更新信号 (table_name, mapping)
    
    def __init__(self, parent=None, max_visible_rows: int = 1000, dynamic_table_manager=None):
        """
        初始化虚拟化可展开表格
        
        Args:
            parent: 父组件
            max_visible_rows: 最大可见行数，用于性能优化，默认1000行
            dynamic_table_manager: 动态表格管理器实例
        """
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 核心依赖注入
        if dynamic_table_manager is None:
            raise ValueError("dynamic_table_manager 实例是必需的")
        self.dynamic_table_manager = dynamic_table_manager

        # 数据模型
        self.model = ExpandableTableModel(self)
        
        # 组件管理器初始化
        self.animation_manager = TableAnimationManager(self)
        self.cell_editor = TableCellEditor(self)
        self.context_menu = TableContextMenu(self)
        self.shortcut_manager = KeyboardShortcutManager(self)
        self.drag_sort_manager = DragSortManager(self)  # P4-005: 拖拽排序管理器
        
        # 表格状态管理
        self.visible_rows = []
        self.selected_rows = set()
        self.selected_rows_set = set()  # 多选状态管理
        self.selection_anchor = -1      # 选择锚点
        self.last_selected_row = -1
        self.multi_select_enabled = True
        self.batch_operation_mode = False
        self.expanded_rows = set()      # 展开的行集合
        
        # 编辑状态管理 (P4-001)
        self.edit_mode_enabled = True
        self.current_edit_data = None
        self.current_edit_cell = None   # 当前编辑的单元格 (row, column)
        self.edit_history = []
        self.edit_trigger_delay = 500   # 双击编辑触发延迟(ms)
        self.last_click_time = 0        # 上次点击时间
        self.last_click_pos = (-1, -1)  # 上次点击位置
        
        # 右键菜单状态 (P4-003)
        self.context_menu_enabled = True
        self.last_context_position = QPoint()
        self.last_right_click_pos = QPoint()
        self.last_right_click_time = 0
        
        # 快捷键状态 (P4-004)
        self.shortcuts_enabled = True
        
        # 拖拽排序状态 (P4-005)
        self.drag_sort_enabled = True
        self.drag_start_position = QPoint()
        self.is_potential_drag = False
        
        # 字段映射编辑状态
        self.config_sync_manager = ConfigSyncManager()
        self.header_edit_manager = HeaderEditManager(self.config_sync_manager)
        self.current_table_name = "default_table"  # 默认表名
        self.header_edit_enabled = True
        
        # 性能配置
        self.scroll_buffer_size = 10
        self.animation_fps = 60
        self.render_batch_size = 50
        self.max_visible_rows = max_visible_rows  # 最大可见行数，用于性能优化
        self.default_row_height = 30             # 默认行高
        self.expanded_row_height = 120           # 展开行的额外高度
        
        # UI初始化
        self.setup_ui()
        self.setup_connections()
        
        self.logger.info(f"虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: {self.max_visible_rows}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 基本配置
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.MultiSelection)
        self.setShowGrid(True)
        self.setSortingEnabled(True)
        
        # 拖拽排序配置 (P4-005)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)
        self.setDragDropMode(QAbstractItemView.InternalMove)
        self.setDefaultDropAction(Qt.MoveAction)
        
        # 表头配置
        self.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.horizontalHeader().setStretchLastSection(True)
        self.verticalHeader().setVisible(False)
        
        # 表头双击编辑配置
        self.horizontalHeader().sectionDoubleClicked.connect(self._on_header_double_clicked)
        
        # Material Design样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #E0E0E0;
                background-color: #FAFAFA;
                alternate-background-color: #F5F5F5;
                selection-background-color: #E3F2FD;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
            }
            
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2196F3, stop: 1 #1976D2
                );
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 13px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E0E0E0;
            }
            
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: #1565C0;
            }
            
            QTableWidget::item:hover {
                background-color: #F3E5F5;
            }
            
            /* 拖拽状态样式 (P4-005) */
            QTableWidget[dragging="true"] {
                border: 2px solid #2196F3;
                background-color: #F3E5F5;
            }
            
            QTableWidget::item[drag_target="true"] {
                background-color: #BBDEFB;
                border: 2px dashed #2196F3;
            }
        """)
    
    def setup_connections(self):
        """设置信号连接"""
        # 动画管理器连接
        self.animation_manager.animation_finished.connect(
            self._on_animation_finished
        )
        
        # 单元格编辑器连接 (P4-001)
        self.cell_editor.edit_finished.connect(self._on_edit_finished)
        
        # 右键菜单连接 (P4-003)
        self.context_menu.action_triggered.connect(self._on_context_action_triggered)
        
        # 快捷键管理器连接 (P4-004)
        self.shortcut_manager.shortcut_triggered.connect(self._on_shortcut_triggered)
        self.shortcut_manager.help_requested.connect(self._on_shortcut_help_requested)
        
        # 拖拽排序管理器连接 (P4-005)
        self.drag_sort_manager.drag_started.connect(self._on_drag_started)
        self.drag_sort_manager.drag_moved.connect(self._on_drag_moved)
        self.drag_sort_manager.drag_completed.connect(self._on_drag_completed)
        self.drag_sort_manager.drag_cancelled.connect(self._on_drag_cancelled)
        
        # 表格事件连接
        self.cellClicked.connect(self._on_cell_clicked)
        self.cellDoubleClicked.connect(self._on_cell_double_clicked)
        self.itemSelectionChanged.connect(self._on_selection_changed)
    
    def set_data(self, data: List[Dict[str, Any]], headers: List[str], auto_adjust_visible_rows: bool = True):
        """
        设置表格数据
        
        Args:
            data: 行数据列表
            headers: 表头列表
            auto_adjust_visible_rows: 是否根据数据量自动调整最大可见行数，默认为True
        """
        start_time = time.time()
        
        try:
            # 自动调整最大可见行数
            if auto_adjust_visible_rows:
                self.auto_adjust_max_visible_rows(len(data))
            
            # 更新数据模型
            self.model.set_data(data, headers)
            
            # 设置表头
            self.setColumnCount(len(headers))
            self.setHorizontalHeaderLabels(headers)
            
            # **新增：智能字段映射应用逻辑**
            if hasattr(self, 'current_table_name') and self.current_table_name:
                # 首先尝试应用现有的字段映射
                self._refresh_header_display()
                
                # 检查是否需要自动生成映射（当前映射不完整或不存在时）
                self._auto_generate_field_mapping_if_needed(headers)
                
                # 再次刷新表头显示，确保新生成的映射被应用
                self._refresh_header_display()
            
            # 设置行数
            self.setRowCount(len(data))
            
            # 填充可见数据
            self._populate_visible_data()
            
            # 调整列宽
            self._adjust_column_widths()
            
            elapsed_time = (time.time() - start_time) * 1000
            self.logger.info(f"表格数据设置完成: {len(data)} 行, 最大可见行数: {self.max_visible_rows}, 耗时: {elapsed_time:.2f}ms")
            
        except Exception as e:
            self.logger.error(f"设置表格数据失败: {e}")
    
    def _auto_generate_field_mapping_if_needed(self, headers: List[str]):
        """
        根据需要自动生成字段映射
        
        Args:
            headers: 表头列表（原始字段名）
        """
        try:
            # 如果表头为空，直接返回，避免在无字段情况下执行耗时操作
            if not headers:
                self.logger.debug("表头为空，跳过自动字段映射生成。")
                return

            # 如果当前表名为 default_table（空表），也跳过自动映射生成
            if getattr(self, 'current_table_name', '') == 'default_table':
                self.logger.debug("当前表为 default_table，跳过自动字段映射生成。")
                return
            
            if not hasattr(self, 'current_table_name') or not self.current_table_name:
                return
            
            # 检查当前映射状态
            existing_mapping = self.config_sync_manager.load_mapping(self.current_table_name)
            
            # 计算映射覆盖率
            coverage_ratio = 0.0
            chinese_mapping_count = 0
            
            if existing_mapping:
                # 检查有多少字段有映射
                mapped_count = sum(1 for field in headers if field in existing_mapping)
                coverage_ratio = mapped_count / len(headers) if headers else 0.0
                
                # 检查有多少字段映射为中文
                chinese_mapping_count = sum(
                    1 for field in headers 
                    if field in existing_mapping and existing_mapping[field] != field
                    and not all(ord(c) < 128 for c in existing_mapping[field])  # 检查是否包含非ASCII字符（中文）
                )
            
            # 决定是否需要自动生成映射
            needs_generation = (
                not existing_mapping or  # 没有映射配置
                coverage_ratio < 0.8 or  # 覆盖率低于80%
                chinese_mapping_count / len(headers) < 0.5  # 中文映射比例低于50%
            )
            
            if needs_generation:
                self.logger.info(f"表 {self.current_table_name} 需要自动生成字段映射: 覆盖率={coverage_ratio:.1%}, 中文映射={chinese_mapping_count}/{len(headers)}")
                
                # 尝试导入自动映射生成器
                try:
                    from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator
                    
                    # 初始化生成器
                    generator = AutoFieldMappingGenerator()
                    
                    # 从数据库获取真实的字段名和描述
                    db_fields, db_field_descriptions = self.dynamic_table_manager.get_table_columns_with_descriptions(self.current_table_name)

                    # 基于数据库字段生成映射
                    new_mapping = generator.generate_mapping_from_db_fields(
                        table_name=self.current_table_name,
                        db_fields=db_fields,
                        db_field_descriptions=db_field_descriptions
                    )
                    
                    if new_mapping:
                        # 保存生成的映射
                        self.config_sync_manager.save_mapping(self.current_table_name, new_mapping)
                        
                        # 发出映射更新信号
                        self.field_mapping_updated.emit(self.current_table_name, new_mapping)
                        
                        self.logger.info(f"为表 {self.current_table_name} 自动生成了字段映射: {len(new_mapping)} 个字段")
                        
                        # 记录生成的映射详情（用于调试）
                        for orig, mapped in new_mapping.items():
                            if orig != mapped:
                                self.logger.debug(f"  字段映射: {orig} -> {mapped}")
                    else:
                        self.logger.info(f"为表 {self.current_table_name} 生成的字段映射为空，跳过更新。")
                    
                except ImportError as e:
                    self.logger.warning(f"无法导入AutoFieldMappingGenerator: {e}")
                except Exception as e:
                    self.logger.error(f"自动生成字段映射失败: {e}")
            else:
                self.logger.debug(f"表 {self.current_table_name} 字段映射状态良好，无需重新生成")
            
        except Exception as e:
            self.logger.error(f"检查字段映射需求失败: {e}")
    
    def _populate_visible_data(self):
        """填充可见区域数据"""
        try:
            row_count = self.model.rowCount()
            col_count = self.model.columnCount()
            
            for row in range(min(row_count, self.max_visible_rows)):
                for col in range(col_count):
                    index = self.model.index(row, col)
                    data = self.model.data(index, Qt.DisplayRole)
                    
                    item = QTableWidgetItem(str(data))
                    
                    # 根据数据类型设置对齐方式
                    if isinstance(data, (int, float)):
                        item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    else:
                        item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                    
                    self.setItem(row, col, item)
            
        except Exception as e:
            self.logger.error(f"填充可见数据失败: {e}")
    
    def _adjust_column_widths(self):
        """自动调整列宽"""
        try:
            header = self.horizontalHeader()
            
            # 为前几列设置固定宽度，最后一列自动拉伸
            for col in range(self.columnCount() - 1):
                header.setSectionResizeMode(col, QHeaderView.ResizeToContents)
            
            # 最后一列拉伸填充
            header.setSectionResizeMode(
                self.columnCount() - 1, QHeaderView.Stretch
            )
            
        except Exception as e:
            self.logger.error(f"调整列宽失败: {e}")
    
    def _on_cell_clicked(self, row: int, column: int):
        """处理单击事件 - 支持多选"""
        try:
            current_time = time.time() * 1000  # 转换为毫秒
            
            # 记录点击信息用于双击判断
            self.last_click_time = current_time
            self.last_click_pos = (row, column)
            
            # 获取修饰键状态
            modifiers = QApplication.keyboardModifiers()
            ctrl_pressed = modifiers & Qt.ControlModifier
            shift_pressed = modifiers & Qt.ShiftModifier
            
            # 根据修饰键状态决定选择行为
            if self.multi_select_enabled and ctrl_pressed:
                self._handle_ctrl_click(row)
            elif self.multi_select_enabled and shift_pressed:
                self._handle_shift_click(row)
            else:
                self._handle_normal_click(row, column)
            
            # 更新选择状态
            self._update_selection_display()
            
            self.logger.debug(f"单击单元格: 行{row}, 列{column}, 修饰键: Ctrl={ctrl_pressed}, Shift={shift_pressed}")
            
        except Exception as e:
            self.logger.error(f"处理单击事件失败: 行{row}, 列{column}, 错误: {e}")
    
    def _handle_normal_click(self, row: int, column: int):
        """处理普通单击 - 单选"""
        try:
            # 清除之前的选择
            self.selected_rows_set.clear()
            self.clearSelection()
            
            # 选择当前行
            self.selected_rows_set.add(row)
            self.selection_anchor = row
            self.last_selected_row = row
            
            # 如果点击第0列，处理展开/折叠
            if column == 0:
                self.toggle_row_expansion(row)
            
        except Exception as e:
            self.logger.error(f"处理普通单击失败: 行{row}, 列{column}, 错误: {e}")
    
    def _handle_ctrl_click(self, row: int):
        """处理Ctrl+单击 - 非连续多选"""
        try:
            if row in self.selected_rows_set:
                # 如果已选中，则取消选择
                self.selected_rows_set.remove(row)
            else:
                # 如果未选中，则添加选择
                self.selected_rows_set.add(row)
                self.selection_anchor = row
            
            self.last_selected_row = row
            
            self.logger.debug(f"Ctrl+单击: 行{row}, 当前选中: {len(self.selected_rows_set)}行")
            
        except Exception as e:
            self.logger.error(f"处理Ctrl+单击失败: 行{row}, 错误: {e}")
    
    def _handle_shift_click(self, row: int):
        """处理Shift+单击 - 连续范围选择"""
        try:
            if self.selection_anchor == -1:
                # 没有锚点，当作普通单击
                self._handle_normal_click(row, 0)
                return
            
            # 确定选择范围
            start_row = min(self.selection_anchor, row)
            end_row = max(self.selection_anchor, row)
            
            # 清除之前的选择
            self.selected_rows_set.clear()
            
            # 选择范围内的所有行
            for r in range(start_row, end_row + 1):
                if r < self.rowCount():
                    self.selected_rows_set.add(r)
            
            self.last_selected_row = row
            
            self.logger.debug(f"Shift+单击: 范围 {start_row}-{end_row}, 选中: {len(self.selected_rows_set)}行")
            
        except Exception as e:
            self.logger.error(f"处理Shift+单击失败: 行{row}, 错误: {e}")
    
    def _update_selection_display(self):
        """更新选择显示状态"""
        try:
            # 清除当前选择显示
            self.clearSelection()
            
            # 设置选中行的视觉状态
            for row in self.selected_rows_set:
                if row < self.rowCount():
                    for column in range(self.columnCount()):
                        item = self.item(row, column)
                        if item:
                            item.setSelected(True)
            
            # 发出选择变化信号
            selected_list = list(self.selected_rows_set)
            self.selection_changed.emit(selected_list)
            
        except Exception as e:
            self.logger.error(f"更新选择显示失败: {e}")
    
    def select_all_rows(self):
        """全选所有行"""
        try:
            self.selected_rows_set.clear()
            for row in range(self.rowCount()):
                self.selected_rows_set.add(row)
            
            self._update_selection_display()
            self.logger.debug(f"全选: {len(self.selected_rows_set)}行")
            
        except Exception as e:
            self.logger.error(f"全选失败: {e}")
    
    def clear_selection(self):
        """清除所有选择"""
        try:
            self.selected_rows_set.clear()
            self.clearSelection()
            self.selection_anchor = -1
            self.last_selected_row = -1
            
            self.selection_changed.emit([])
            self.logger.debug("清除所有选择")
            
        except Exception as e:
            self.logger.error(f"清除选择失败: {e}")
    
    def get_selected_rows_count(self) -> int:
        """获取选中行数量"""
        return len(self.selected_rows_set)
    
    def get_selected_rows_list(self) -> List[int]:
        """获取选中行列表"""
        return sorted(list(self.selected_rows_set))
    
    def is_row_selected(self, row: int) -> bool:
        """检查行是否被选中"""
        return row in self.selected_rows_set
    
    def set_multi_select_enabled(self, enabled: bool):
        """设置多选模式是否启用"""
        try:
            self.multi_select_enabled = enabled
            if not enabled:
                # 禁用多选时，清除多选状态
                if len(self.selected_rows_set) > 1:
                    # 保留最后选择的行
                    if self.last_selected_row != -1:
                        self.selected_rows_set = {self.last_selected_row}
                    else:
                        self.clear_selection()
                    self._update_selection_display()
            
            self.logger.debug(f"多选模式设置为: {'启用' if enabled else '禁用'}")
            
        except Exception as e:
            self.logger.error(f"设置多选模式失败: {e}")
    
    def get_batch_operation_mode(self) -> bool:
        """获取批量操作模式状态"""
        return self.batch_operation_mode
    
    def set_batch_operation_mode(self, enabled: bool):
        """设置批量操作模式"""
        try:
            self.batch_operation_mode = enabled
            
            # 批量操作模式下的视觉提示
            if enabled:
                self.setStyleSheet("""
                    QTableWidget::item:selected {
                        background-color: #ffeb3b;
                        color: #1976d2;
                        border: 2px solid #ff9800;
                    }
                """)
            else:
                # 恢复正常选择样式
                self.setStyleSheet("")
            
            self.logger.debug(f"批量操作模式设置为: {'启用' if enabled else '禁用'}")
            
        except Exception as e:
            self.logger.error(f"设置批量操作模式失败: {e}")
    
    def _on_cell_double_clicked(self, row: int, column: int):
        """处理双击事件 - 智能编辑/展开切换"""
        try:
            current_time = time.time() * 1000
            
            # 检查是否是有效的双击
            if (self.last_click_pos == (row, column) and 
                current_time - self.last_click_time < self.edit_trigger_delay):
                
                # 决定是编辑还是展开
                if self._should_edit_cell(row, column):
                    self._start_cell_editing(row, column)
                else:
                    self._handle_row_expansion(row, column)
            else:
                # 单独的双击，默认行为
                self._handle_row_expansion(row, column)
            
        except Exception as e:
            self.logger.error(f"处理双击事件失败: 行{row}, 列{column}, 错误: {e}")
    
    def _should_edit_cell(self, row: int, column: int) -> bool:
        """判断是否应该编辑单元格"""
        try:
            # 检查编辑模式是否启用
            if not self.edit_mode_enabled:
                return False
            
            # 检查是否有其他单元格正在编辑
            if self.current_edit_cell is not None:
                return False
            
            # 检查列是否可编辑 (第0列通常是展开/折叠控制列)
            if column == 0:
                return False
            
            # 检查单元格内容是否可编辑
            item = self.item(row, column)
            if item and item.flags() & Qt.ItemIsEditable:
                return True
            
            return True  # 默认可编辑
            
        except Exception as e:
            self.logger.error(f"判断单元格是否可编辑失败: 行{row}, 列{column}, 错误: {e}")
            return False
    
    def _start_cell_editing(self, row: int, column: int):
        """开始单元格编辑"""
        try:
            # 设置当前编辑单元格
            self.current_edit_cell = (row, column)
            
            # 获取原始值
            item = self.item(row, column)
            original_value = item.text() if item else ""
            
            # 开始编辑
            self.editItem(item)
            
            # 发出编辑模式变化信号
            self.edit_mode_changed.emit(True)
            
            self.logger.debug(f"开始编辑单元格: 行{row}, 列{column}, 原值: {original_value}")
            
        except Exception as e:
            self.logger.error(f"开始单元格编辑失败: 行{row}, 列{column}, 错误: {e}")
    
    def _handle_row_expansion(self, row: int, column: int):
        """处理行展开/折叠"""
        try:
            # 如果在第0列或者不可编辑列，直接切换展开状态
            self.toggle_row_expansion(row)
            
        except Exception as e:
            self.logger.error(f"处理行展开失败: 行{row}, 列{column}, 错误: {e}")
    
    def _on_selection_changed(self):
        """处理选择变化"""
        try:
            selected_rows = []
            for item in self.selectedItems():
                if item.row() not in selected_rows:
                    selected_rows.append(item.row())
            
            self.selected_rows = set(selected_rows)
            self.selection_changed.emit(selected_rows)
            
        except Exception as e:
            self.logger.error(f"处理选择变化失败: {e}")
    
    def toggle_row_expansion(self, row: int) -> bool:
        """
        切换行展开状态
        
        Args:
            row: 行索引
            
        Returns:
            bool: 操作是否成功
        """
        try:
            row_data = self.model.get_row_data(row)
            if not row_data:
                return False
            
            # 检查动画状态
            if self.animation_manager.is_animating(row_data.id):
                self.logger.debug(f"行 {row} 正在动画中，跳过操作")
                return False
            
            # 根据当前状态决定操作
            if row_data.expand_state == ExpandState.COLLAPSED:
                return self._expand_row(row)
            elif row_data.expand_state == ExpandState.EXPANDED:
                return self._collapse_row(row)
            
            return False
            
        except Exception as e:
            self.logger.error(f"切换行展开状态失败: 行{row}, 错误: {e}")
            return False
    
    def _expand_row(self, row: int) -> bool:
        """展开指定行"""
        try:
            row_data = self.model.get_row_data(row)
            if not row_data:
                return False
            
            # 更新状态
            self.model.update_expand_state(
                row, ExpandState.EXPANDING, 0, 0.0
            )
            self.expanded_rows.add(row)
            
            # 启动展开动画
            success = self.animation_manager.start_expand_animation(
                row_data.id,
                self.expanded_row_height,
                lambda row_id, progress: self._update_expand_progress(row, progress)
            )
            
            if success:
                self.logger.debug(f"开始展开行: {row}")
                return True
            else:
                # 动画启动失败，回退状态
                self.model.update_expand_state(
                    row, ExpandState.COLLAPSED, 0, 0.0
                )
                self.expanded_rows.discard(row)
                return False
                
        except Exception as e:
            self.logger.error(f"展开行失败: 行{row}, 错误: {e}")
            return False
    
    def _collapse_row(self, row: int) -> bool:
        """折叠指定行"""
        try:
            row_data = self.model.get_row_data(row)
            if not row_data:
                return False
            
            # 更新状态
            self.model.update_expand_state(
                row, ExpandState.COLLAPSING, row_data.expand_height, 1.0
            )
            
            # 启动折叠动画
            success = self.animation_manager.start_collapse_animation(
                row_data.id,
                row_data.expand_height,
                lambda row_id, progress: self._update_collapse_progress(row, progress)
            )
            
            if success:
                self.logger.debug(f"开始折叠行: {row}")
                return True
            else:
                # 动画启动失败，保持展开状态
                self.model.update_expand_state(
                    row, ExpandState.EXPANDED, self.expanded_row_height, 1.0
                )
                return False
                
        except Exception as e:
            self.logger.error(f"折叠行失败: 行{row}, 错误: {e}")
            return False
    
    def _update_expand_progress(self, row: int, progress: float):
        """更新展开进度"""
        try:
            current_height = int(self.expanded_row_height * progress)
            self.model.update_expand_state(
                row, ExpandState.EXPANDING, current_height, progress
            )
            
            # 更新行高
            self.setRowHeight(row, self.default_row_height + current_height)
            
        except Exception as e:
            self.logger.error(f"更新展开进度失败: 行{row}, 进度{progress}, 错误: {e}")
    
    def _update_collapse_progress(self, row: int, progress: float):
        """更新折叠进度"""
        try:
            current_height = int(self.expanded_row_height * (1.0 - progress))
            self.model.update_expand_state(
                row, ExpandState.COLLAPSING, current_height, progress
            )
            
            # 更新行高
            self.setRowHeight(row, self.default_row_height + current_height)
            
        except Exception as e:
            self.logger.error(f"更新折叠进度失败: 行{row}, 进度{progress}, 错误: {e}")
    
    def _on_animation_finished(self, row_id: str, final_state: ExpandState):
        """动画完成处理"""
        try:
            # 查找对应的行索引
            row = -1
            for i, row_data in enumerate(self.model.row_data):
                if row_data.id == row_id:
                    row = i
                    break
            
            if row < 0:
                return
            
            # 更新最终状态
            if final_state == ExpandState.EXPANDED:
                self.model.update_expand_state(
                    row, ExpandState.EXPANDED, self.expanded_row_height, 1.0
                )
                self.setRowHeight(row, self.default_row_height + self.expanded_row_height)
                self.row_expanded.emit(row, self.model.get_row_data(row).detail_data)
                
            elif final_state == ExpandState.COLLAPSED:
                self.model.update_expand_state(
                    row, ExpandState.COLLAPSED, 0, 0.0
                )
                self.setRowHeight(row, self.default_row_height)
                self.expanded_rows.discard(row)
                self.row_collapsed.emit(row, self.model.get_row_data(row).main_data)
            
            self.logger.debug(f"动画完成处理: {row_id}, 最终状态: {final_state.value}")
            
        except Exception as e:
            self.logger.error(f"动画完成处理失败: {row_id}, 错误: {e}")
    
    def get_expanded_rows(self) -> List[int]:
        """获取所有展开的行"""
        return list(self.expanded_rows)
    
    def get_selected_rows(self) -> List[int]:
        """获取所有选中的行"""
        return list(self.selected_rows)
    
    def expand_all_rows(self):
        """展开所有行"""
        for row in range(self.rowCount()):
            if row not in self.expanded_rows:
                self.toggle_row_expansion(row)
    
    def collapse_all_rows(self):
        """折叠所有行"""
        for row in list(self.expanded_rows):
            self.toggle_row_expansion(row)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'expanded_rows_count': len(self.expanded_rows),
            'total_rows': self.rowCount(),
            'max_visible_rows': self.max_visible_rows,
            'animation_stats': self.animation_manager.performance_stats,
            'memory_usage': {
                'data_cache_size': len(self.model.data_cache),
                'active_animations': len(self.animation_manager.active_animations)
            }
        }

    def set_max_visible_rows(self, max_rows: int) -> bool:
        """
        设置最大可见行数
        
        Args:
            max_rows: 最大可见行数，必须大于0
            
        Returns:
            bool: 设置是否成功
        """
        try:
            if max_rows <= 0:
                self.logger.warning(f"最大可见行数必须大于0，当前值: {max_rows}")
                return False
            
            if max_rows < 10:
                self.logger.warning(f"最大可见行数过小可能影响性能，建议至少设置为10，当前值: {max_rows}")
            
            old_value = self.max_visible_rows
            self.max_visible_rows = max_rows
            
            # 如果新值小于旧值，需要重新填充可见数据
            if max_rows < old_value:
                self._populate_visible_data()
            
            self.logger.info(f"最大可见行数已更新: {old_value} -> {max_rows}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置最大可见行数失败: {e}")
            return False
    
    def get_max_visible_rows(self) -> int:
        """
        获取当前最大可见行数
        
        Returns:
            int: 当前最大可见行数
        """
        return self.max_visible_rows
    
    def auto_adjust_max_visible_rows(self, data_count: int) -> bool:
        """
        根据数据量自动调整最大可见行数
        
        Args:
            data_count: 数据总行数
            
        Returns:
            bool: 调整是否成功
        """
        try:
            # 如果数据量为0，不进行调整，保持当前设置
            if data_count <= 0:
                self.logger.info(f"数据量为{data_count}，不调整最大可见行数，保持当前值: {self.max_visible_rows}")
                return True
            
            # 自动调整算法：
            # - 小于100行：显示全部
            # - 100-1000行：显示50%
            # - 1000-10000行：显示10%
            # - 大于10000行：最多显示1000行
            
            if data_count <= 100:
                new_max = data_count
            elif data_count <= 1000:
                new_max = max(100, data_count // 2)
            elif data_count <= 10000:
                new_max = max(500, data_count // 10)
            else:
                new_max = 1000
            
            # 确保不超过原设定的上限
            original_max = 1000  # 原始默认值
            new_max = min(new_max, original_max)
            
            success = self.set_max_visible_rows(new_max)
            if success:
                self.logger.info(f"根据数据量({data_count})自动调整最大可见行数为: {new_max}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"自动调整最大可见行数失败: {e}")
            return False
    
    def _on_edit_finished(self, row: int, column: int, value: Any, is_valid: bool):
        """处理编辑完成"""
        try:
            # 获取原始值
            item = self.item(row, column)
            old_value = item.text() if item else ""
            
            if is_valid and value is not None:
                # 更新单元格值
                if item:
                    item.setText(str(value))
                else:
                    new_item = QTableWidgetItem(str(value))
                    self.setItem(row, column, new_item)
                
                # 发出编辑完成信号
                self.cell_edited.emit(row, column, old_value, value)
                
                self.logger.debug(f"单元格编辑完成: 行{row}, 列{column}, 旧值: {old_value}, 新值: {value}")
            else:
                self.logger.debug(f"单元格编辑取消: 行{row}, 列{column}")
            
            # 清除编辑状态
            self.current_edit_cell = None
            self.edit_mode_changed.emit(False)
            
        except Exception as e:
            self.logger.error(f"处理编辑完成失败: 行{row}, 列{column}, 错误: {e}")

    def set_edit_mode_enabled(self, enabled: bool):
        """设置编辑模式是否启用"""
        try:
            self.edit_mode_enabled = enabled
            self.logger.debug(f"编辑模式设置为: {'启用' if enabled else '禁用'}")
            
        except Exception as e:
            self.logger.error(f"设置编辑模式失败: {e}")
    
    def is_editing(self) -> bool:
        """检查是否正在编辑"""
        return self.current_edit_cell is not None
    
    def cancel_current_edit(self):
        """取消当前编辑"""
        try:
            if self.current_edit_cell:
                row, column = self.current_edit_cell
                self.closePersistentEditor(self.item(row, column))
                self.current_edit_cell = None
                self.edit_mode_changed.emit(False)
                self.logger.debug("取消当前编辑")
                
        except Exception as e:
            self.logger.error(f"取消编辑失败: {e}")
    
    # ==================== 批量操作功能 ====================
    
    def batch_edit_cells(self, column: int, new_value: Any) -> bool:
        """
        批量编辑选中行的指定列
        
        Args:
            column: 要编辑的列索引
            new_value: 新值
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量编辑")
                return False
            
            affected_rows = []
            
            # 对每个选中的行进行编辑
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount() and column < self.columnCount():
                    # 获取原值
                    item = self.item(row, column)
                    old_value = item.text() if item else ""
                    
                    # 设置新值
                    if item:
                        item.setText(str(new_value))
                    else:
                        new_item = QTableWidgetItem(str(new_value))
                        self.setItem(row, column, new_item)
                    
                    # 发出编辑信号
                    self.cell_edited.emit(row, column, old_value, new_value)
                    affected_rows.append(row)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_edit", len(affected_rows), affected_rows)
            
            self.logger.info(f"批量编辑完成: 列{column}, 影响{len(affected_rows)}行, 新值: {new_value}")
            return True
            
        except Exception as e:
            self.logger.error(f"批量编辑失败: 列{column}, 新值{new_value}, 错误: {e}")
            return False
    
    def batch_delete_rows(self) -> bool:
        """
        批量删除选中的行
        
        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量删除")
                return False
            
            # 获取要删除的行列表，按降序排列（从后往前删除）
            rows_to_delete = sorted(self.selected_rows_set, reverse=True)
            affected_rows = rows_to_delete.copy()
            
            # 删除行
            for row in rows_to_delete:
                if row < self.rowCount():
                    self.removeRow(row)
            
            # 清除选择状态
            self.clear_selection()
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_delete", len(affected_rows), affected_rows)
            
            self.logger.info(f"批量删除完成: 删除{len(affected_rows)}行")
            return True
            
        except Exception as e:
            self.logger.error(f"批量删除失败: {e}")
            return False
    
    def batch_expand_rows(self) -> bool:
        """
        批量展开选中的行
        
        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量展开")
                return False
            
            affected_rows = []
            
            # 展开所有选中的行
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount():
                    row_data = self.model.get_row_data(row)
                    if row_data and row_data.expand_state == ExpandState.COLLAPSED:
                        if self._expand_row(row):
                            affected_rows.append(row)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_expand", len(affected_rows), affected_rows)
            
            self.logger.info(f"批量展开完成: 展开{len(affected_rows)}行")
            return True
            
        except Exception as e:
            self.logger.error(f"批量展开失败: {e}")
            return False
    
    def batch_collapse_rows(self) -> bool:
        """
        批量折叠选中的行
        
        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量折叠")
                return False
            
            affected_rows = []
            
            # 折叠所有选中的行
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount():
                    row_data = self.model.get_row_data(row)
                    if row_data and row_data.expand_state == ExpandState.EXPANDED:
                        if self._collapse_row(row):
                            affected_rows.append(row)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_collapse", len(affected_rows), affected_rows)
            
            self.logger.info(f"批量折叠完成: 折叠{len(affected_rows)}行")
            return True
            
        except Exception as e:
            self.logger.error(f"批量折叠失败: {e}")
            return False
    
    def batch_copy_data(self) -> List[List[str]]:
        """
        批量复制选中行的数据
        
        Returns:
            List[List[str]]: 复制的数据矩阵
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量复制")
                return []
            
            copied_data = []
            
            # 复制所有选中行的数据
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount():
                    row_data = []
                    for column in range(self.columnCount()):
                        item = self.item(row, column)
                        cell_value = item.text() if item else ""
                        row_data.append(cell_value)
                    copied_data.append(row_data)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_copy", len(copied_data), list(self.selected_rows_set))
            
            self.logger.info(f"批量复制完成: 复制{len(copied_data)}行数据")
            return copied_data
            
        except Exception as e:
            self.logger.error(f"批量复制失败: {e}")
            return []
    
    def batch_export_data(self, include_headers: bool = True) -> Dict[str, Any]:
        """
        批量导出选中行的数据
        
        Args:
            include_headers: 是否包含表头
            
        Returns:
            Dict: 导出的数据结构
        """
        try:
            if not self.selected_rows_set:
                self.logger.warning("没有选中的行，无法执行批量导出")
                return {}
            
            export_data = {
                'headers': [],
                'rows': [],
                'metadata': {
                    'export_time': time.time(),
                    'total_rows': len(self.selected_rows_set),
                    'selected_rows': sorted(list(self.selected_rows_set))
                }
            }
            
            # 添加表头
            if include_headers:
                for column in range(self.columnCount()):
                    header_item = self.horizontalHeaderItem(column)
                    header_text = header_item.text() if header_item else f"列{column}"
                    export_data['headers'].append(header_text)
            
            # 添加数据行
            for row in sorted(self.selected_rows_set):
                if row < self.rowCount():
                    row_data = []
                    for column in range(self.columnCount()):
                        item = self.item(row, column)
                        cell_value = item.text() if item else ""
                        row_data.append(cell_value)
                    export_data['rows'].append(row_data)
            
            # 发出批量操作完成信号
            self.batch_operation_completed.emit("batch_export", len(export_data['rows']), list(self.selected_rows_set))
            
            self.logger.info(f"批量导出完成: 导出{len(export_data['rows'])}行数据")
            return export_data
            
        except Exception as e:
            self.logger.error(f"批量导出失败: {e}")
            return {}
    
    def get_batch_operation_stats(self) -> Dict[str, Any]:
        """获取批量操作统计信息"""
        return {
            'enabled_operations': [
                'edit', 'delete', 'copy', 'export', 'expand', 'collapse'
            ],
            'multi_select_enabled': self.multi_select_enabled,
            'batch_mode_enabled': self.batch_operation_mode,
            'selected_count': len(self.selected_rows_set),
            'max_batch_size': 1000,  # 最大批量处理数量
        }
    
    # ==================== 右键菜单系统方法 ====================
    
    def contextMenuEvent(self, event: QContextMenuEvent):
        """处理右键菜单事件"""
        try:
            if not self.context_menu_enabled:
                super().contextMenuEvent(event)
                return
            
            # 记录右键点击信息
            self.last_right_click_pos = event.pos()
            self.last_right_click_time = time.time() * 1000
            
            # 确定点击的位置和上下文
            context = self._determine_context_menu_context(event.pos())
            
            if context:
                # 发出右键菜单请求信号
                self.context_menu_requested.emit(context, event.globalPos())
                
                # 显示右键菜单
                self.context_menu.show_context_menu(context, event.globalPos())
                
                self.logger.debug(f"显示右键菜单: {context.menu_type.value}")
            else:
                # 如果无法确定上下文，显示默认菜单
                super().contextMenuEvent(event)
                
        except Exception as e:
            self.logger.error(f"处理右键菜单事件失败: {e}")
            super().contextMenuEvent(event)
    
    def _determine_context_menu_context(self, position: QPoint) -> Optional[ContextMenuContext]:
        """确定右键菜单上下文"""
        try:
            # 获取点击的项目
            item = self.itemAt(position)
            
            if item is None:
                # 点击空白区域
                return ContextMenuContext(
                    menu_type=ContextMenuType.EMPTY_AREA,
                    clicked_row=-1,
                    clicked_column=-1,
                    selected_rows=self.get_selected_rows_list(),
                    click_position=position,
                    is_editing=self.is_editing()
                )
            
            # 获取点击的行和列
            clicked_row = item.row()
            clicked_column = item.column()
            
            # 检查是否在表头区域
            header_height = self.horizontalHeader().height()
            if position.y() <= header_height:
                return ContextMenuContext(
                    menu_type=ContextMenuType.HEADER,
                    clicked_row=-1,
                    clicked_column=clicked_column,
                    selected_rows=self.get_selected_rows_list(),
                    click_position=position,
                    is_editing=self.is_editing()
                )
            
            # 检查当前编辑状态
            if self.is_editing() and self.current_edit_cell == (clicked_row, clicked_column):
                return ContextMenuContext(
                    menu_type=ContextMenuType.CELL_EDIT,
                    clicked_row=clicked_row,
                    clicked_column=clicked_column,
                    selected_rows=self.get_selected_rows_list(),
                    click_position=position,
                    is_editing=True
                )
            
            # 检查行的展开状态
            row_expanded = self._is_row_expanded(clicked_row)
            
            # 确定菜单类型
            selected_rows = self.get_selected_rows_list()
            if len(selected_rows) > 1:
                menu_type = ContextMenuType.MULTI_ROW
            elif row_expanded:
                menu_type = ContextMenuType.EXPANDED_ROW
            else:
                menu_type = ContextMenuType.SINGLE_ROW
            
            return ContextMenuContext(
                menu_type=menu_type,
                clicked_row=clicked_row,
                clicked_column=clicked_column,
                selected_rows=selected_rows,
                click_position=position,
                is_editing=self.is_editing(),
                row_expanded=row_expanded
            )
            
        except Exception as e:
            self.logger.error(f"确定右键菜单上下文失败: {e}")
            return None
    
    def _is_row_expanded(self, row: int) -> bool:
        """检查行是否已展开"""
        try:
            if hasattr(self, 'model') and self.model:
                row_data = self.model.get_row_data(row)
                if row_data:
                    return row_data.expand_state == ExpandState.EXPANDED
            return row in self.expanded_rows
        except Exception as e:
            self.logger.error(f"检查行展开状态失败: 行{row}, 错误: {e}")
            return False
    
    def _on_context_action_triggered(self, action_name: str, context: ContextMenuContext):
        """处理右键菜单动作触发"""
        try:
            # 发出信号通知外部
            self.context_action_triggered.emit(action_name, context)
            
            # 内部处理具体动作
            self._handle_context_action(action_name, context)
            
            self.logger.debug(f"处理右键菜单动作: {action_name}")
            
        except Exception as e:
            self.logger.error(f"处理右键菜单动作失败: {action_name}, 错误: {e}")
    
    def _handle_context_action(self, action_name: str, context: ContextMenuContext):
        """处理具体的右键菜单动作"""
        try:
            # 基础操作
            if action_name == "copy":
                self._handle_copy_action(context)
            elif action_name == "paste":
                self._handle_paste_action(context)
            elif action_name == "cut":
                self._handle_cut_action(context)
            elif action_name == "delete":
                self._handle_delete_action(context)
            elif action_name == "select_all":
                self.select_all_rows()
            elif action_name == "clear_selection":
                self.clear_selection()
            
            # 编辑操作
            elif action_name == "edit_cell":
                if context.clicked_row >= 0 and context.clicked_column >= 0:
                    self._start_cell_editing(context.clicked_row, context.clicked_column)
            elif action_name == "batch_edit":
                self._handle_batch_edit_action(context)
            elif action_name == "cancel_edit":
                self.cancel_current_edit()
            
            # 展开操作
            elif action_name == "expand_row":
                if context.clicked_row >= 0:
                    self.toggle_row_expansion(context.clicked_row)
            elif action_name == "collapse_row":
                if context.clicked_row >= 0:
                    self.toggle_row_expansion(context.clicked_row)
            elif action_name == "expand_all":
                self.expand_all_rows()
            elif action_name == "collapse_all":
                self.collapse_all_rows()
            
            # 批量操作
            elif action_name == "batch_delete":
                self.batch_delete_rows()
            elif action_name == "batch_copy":
                self.batch_copy_data()
            elif action_name == "batch_export":
                self.batch_export_data()
            elif action_name == "batch_expand":
                self.batch_expand_rows()
            elif action_name == "batch_collapse":
                self.batch_collapse_rows()
            
            # 表格操作
            elif action_name == "insert_row":
                self._handle_insert_row_action(context)
            elif action_name == "sort_column":
                self._handle_sort_column_action(context)
            elif action_name == "hide_column":
                self._handle_hide_column_action(context)
            elif action_name == "resize_columns":
                self._handle_resize_columns_action(context)
            
            # 高级操作
            elif action_name == "filter_rows":
                self._handle_filter_rows_action(context)
            elif action_name == "refresh_data":
                self._handle_refresh_data_action(context)
            elif action_name == "export_data":
                self.batch_export_data(include_headers=True)
            
        except Exception as e:
            self.logger.error(f"处理右键菜单动作失败: {action_name}, 错误: {e}")
    
    def _handle_copy_action(self, context: ContextMenuContext):
        """处理复制动作"""
        try:
            if len(context.selected_rows) > 1:
                # 多行复制
                data = self.batch_copy_data()
                self.logger.debug(f"复制 {len(data)} 行数据")
            else:
                # 单行复制
                if context.clicked_row >= 0:
                    # 这里可以实现单行复制逻辑
                    self.logger.debug(f"复制行 {context.clicked_row}")
        except Exception as e:
            self.logger.error(f"复制动作处理失败: {e}")
    
    def _handle_paste_action(self, context: ContextMenuContext):
        """处理粘贴动作"""
        try:
            # 这里可以实现粘贴逻辑
            self.logger.debug("执行粘贴操作")
        except Exception as e:
            self.logger.error(f"粘贴动作处理失败: {e}")
    
    def _handle_cut_action(self, context: ContextMenuContext):
        """处理剪切动作"""
        try:
            # 先复制，再删除
            self._handle_copy_action(context)
            self._handle_delete_action(context)
            self.logger.debug("执行剪切操作")
        except Exception as e:
            self.logger.error(f"剪切动作处理失败: {e}")
    
    def _handle_delete_action(self, context: ContextMenuContext):
        """处理删除动作"""
        try:
            if len(context.selected_rows) > 1:
                # 批量删除
                self.batch_delete_rows()
            elif context.clicked_row >= 0:
                # 单行删除
                self.removeRow(context.clicked_row)
                self.logger.debug(f"删除行 {context.clicked_row}")
        except Exception as e:
            self.logger.error(f"删除动作处理失败: {e}")
    
    def _handle_batch_edit_action(self, context: ContextMenuContext):
        """处理批量编辑动作"""
        try:
            if context.clicked_column > 0:  # 跳过第0列（展开/折叠控制列）
                # 这里可以弹出批量编辑对话框
                self.logger.debug(f"批量编辑列 {context.clicked_column}")
        except Exception as e:
            self.logger.error(f"批量编辑动作处理失败: {e}")
    
    def _handle_insert_row_action(self, context: ContextMenuContext):
        """处理插入行动作"""
        try:
            insert_position = context.clicked_row if context.clicked_row >= 0 else self.rowCount()
            self.insertRow(insert_position)
            self.logger.debug(f"在位置 {insert_position} 插入新行")
        except Exception as e:
            self.logger.error(f"插入行动作处理失败: {e}")
    
    def _handle_sort_column_action(self, context: ContextMenuContext):
        """处理排序列动作"""
        try:
            if context.clicked_column >= 0:
                self.sortItems(context.clicked_column)
                self.logger.debug(f"排序列 {context.clicked_column}")
        except Exception as e:
            self.logger.error(f"排序列动作处理失败: {e}")
    
    def _handle_hide_column_action(self, context: ContextMenuContext):
        """处理隐藏列动作"""
        try:
            if context.clicked_column >= 0:
                self.setColumnHidden(context.clicked_column, True)
                self.logger.debug(f"隐藏列 {context.clicked_column}")
        except Exception as e:
            self.logger.error(f"隐藏列动作处理失败: {e}")
    
    def _handle_resize_columns_action(self, context: ContextMenuContext):
        """处理调整列宽动作"""
        try:
            self.resizeColumnsToContents()
            self.logger.debug("调整所有列宽")
        except Exception as e:
            self.logger.error(f"调整列宽动作处理失败: {e}")
    
    def _handle_filter_rows_action(self, context: ContextMenuContext):
        """处理筛选行动作"""
        try:
            # 这里可以实现筛选逻辑
            self.logger.debug("执行行筛选")
        except Exception as e:
            self.logger.error(f"筛选行动作处理失败: {e}")
    
    def _handle_refresh_data_action(self, context: ContextMenuContext):
        """处理刷新数据动作"""
        try:
            # 这里可以实现数据刷新逻辑
            self.logger.debug("刷新表格数据")
        except Exception as e:
            self.logger.error(f"刷新数据动作处理失败: {e}")
    
    def set_context_menu_enabled(self, enabled: bool):
        """设置右键菜单是否启用"""
        try:
            self.context_menu_enabled = enabled
            self.logger.debug(f"右键菜单设置为: {'启用' if enabled else '禁用'}")
        except Exception as e:
            self.logger.error(f"设置右键菜单状态失败: {e}")
    
    def get_context_menu_stats(self) -> Dict[str, Any]:
        """获取右键菜单统计信息"""
        try:
            if hasattr(self.context_menu, 'get_menu_stats'):
                return self.context_menu.get_menu_stats()
            return {}
        except Exception as e:
            self.logger.error(f"获取右键菜单统计失败: {e}")
            return {}
    
    # ==================== 快捷键系统方法 ====================
    
    def _on_shortcut_triggered(self, shortcut_name: str, context_data: Dict[str, Any]):
        """处理快捷键触发"""
        try:
            if not self.shortcuts_enabled:
                self.logger.debug(f"快捷键已禁用，忽略: {shortcut_name}")
                return
            
            # 发出信号通知外部
            self.shortcut_triggered.emit(shortcut_name, context_data)
            
            # 内部处理具体动作
            action = context_data.get('action', shortcut_name)
            self._handle_shortcut_action(action, context_data)
            
            self.logger.debug(f"处理快捷键: {shortcut_name} ({context_data.get('key_sequence')})")
            
        except Exception as e:
            self.logger.error(f"处理快捷键触发失败: {shortcut_name}, 错误: {e}")
    
    def _handle_shortcut_action(self, action: str, context_data: Dict[str, Any]):
        """处理具体的快捷键动作"""
        try:
            # 编辑相关快捷键
            if action == 'edit_cell':
                self._handle_edit_cell_shortcut()
            elif action == 'save_edit':
                self._handle_save_edit_shortcut()
            elif action == 'cancel_edit':
                self.cancel_current_edit()
            
            # 选择相关快捷键
            elif action == 'select_all':
                self.select_all_rows()
            elif action == 'clear_selection':
                self.clear_selection()
            elif action == 'invert_selection':
                self._handle_invert_selection_shortcut()
            
            # 剪贴板相关快捷键
            elif action == 'copy_data':
                self._handle_copy_data_shortcut()
            elif action == 'paste_data':
                self._handle_paste_data_shortcut()
            elif action == 'cut_data':
                self._handle_cut_data_shortcut()
            
            # 展开折叠相关快捷键
            elif action == 'expand_all':
                self.expand_all_rows()
            elif action == 'collapse_all':
                self.collapse_all_rows()
            elif action == 'toggle_current_row':
                self._handle_toggle_current_row_shortcut()
            
            # 批量操作相关快捷键
            elif action == 'batch_delete':
                self.batch_delete_rows()
            elif action == 'batch_export':
                self.batch_export_data()
            
            # 导航相关快捷键
            elif action == 'goto_first_row':
                self._handle_goto_first_row_shortcut()
            elif action == 'goto_last_row':
                self._handle_goto_last_row_shortcut()
            
            # 系统相关快捷键
            elif action == 'refresh_data':
                self._handle_refresh_data_shortcut()
            elif action == 'show_help':
                self._handle_show_help_shortcut()
            
            else:
                self.logger.warning(f"未处理的快捷键动作: {action}")
            
        except Exception as e:
            self.logger.error(f"处理快捷键动作失败: {action}, 错误: {e}")
    
    def _handle_edit_cell_shortcut(self):
        """处理编辑单元格快捷键"""
        try:
            current_row = self.currentRow()
            current_col = self.currentColumn()
            
            if current_row >= 0 and current_col >= 0:
                self._start_cell_editing(current_row, current_col)
            else:
                self.logger.debug("没有选中的单元格，无法开始编辑")
        except Exception as e:
            self.logger.error(f"处理编辑单元格快捷键失败: {e}")
    
    def _handle_save_edit_shortcut(self):
        """处理保存编辑快捷键"""
        try:
            if self.is_editing():
                # 提交当前编辑
                current_row, current_col = self.current_edit_cell
                item = self.item(current_row, current_col)
                if item:
                    self.commitData(item)
                
                # 移动到下一行
                next_row = current_row + 1
                if next_row < self.rowCount():
                    self.setCurrentCell(next_row, current_col)
                    # 如果下一行也可编辑，自动开始编辑
                    if self.edit_mode_enabled:
                        self._start_cell_editing(next_row, current_col)
            else:
                self.logger.debug("当前没有正在编辑的单元格")
        except Exception as e:
            self.logger.error(f"处理保存编辑快捷键失败: {e}")
    
    def _handle_invert_selection_shortcut(self):
        """处理反选快捷键"""
        try:
            all_rows = set(range(self.rowCount()))
            selected_rows = self.selected_rows_set.copy()
            
            # 反选：选中未选中的，取消选中已选中的
            new_selection = all_rows - selected_rows
            
            self.selected_rows_set = new_selection
            self._update_selection_display()
            
            self.logger.debug(f"反选完成: 选中 {len(new_selection)} 行")
        except Exception as e:
            self.logger.error(f"处理反选快捷键失败: {e}")
    
    def _handle_copy_data_shortcut(self):
        """处理复制数据快捷键"""
        try:
            if self.selected_rows_set:
                copied_data = self.batch_copy_data()
                # 这里可以将数据复制到剪贴板
                self.logger.debug(f"复制数据: {len(copied_data)} 行")
            else:
                self.logger.debug("没有选中的数据可复制")
        except Exception as e:
            self.logger.error(f"处理复制数据快捷键失败: {e}")
    
    def _handle_paste_data_shortcut(self):
        """处理粘贴数据快捷键"""
        try:
            # 这里可以实现从剪贴板粘贴数据的逻辑
            self.logger.debug("粘贴数据功能待实现")
        except Exception as e:
            self.logger.error(f"处理粘贴数据快捷键失败: {e}")
    
    def _handle_cut_data_shortcut(self):
        """处理剪切数据快捷键"""
        try:
            if self.selected_rows_set:
                # 先复制
                self._handle_copy_data_shortcut()
                # 再删除
                self.batch_delete_rows()
                self.logger.debug("剪切数据完成")
            else:
                self.logger.debug("没有选中的数据可剪切")
        except Exception as e:
            self.logger.error(f"处理剪切数据快捷键失败: {e}")
    
    def _handle_toggle_current_row_shortcut(self):
        """处理切换当前行展开状态快捷键"""
        try:
            current_row = self.currentRow()
            if current_row >= 0:
                self.toggle_row_expansion(current_row)
            else:
                self.logger.debug("没有当前行可切换展开状态")
        except Exception as e:
            self.logger.error(f"处理切换行展开状态快捷键失败: {e}")
    
    def _handle_goto_first_row_shortcut(self):
        """处理跳转到第一行快捷键"""
        try:
            if self.rowCount() > 0:
                self.setCurrentCell(0, 0)
                self.scrollToTop()
                self.logger.debug("跳转到第一行")
            else:
                self.logger.debug("表格为空，无法跳转")
        except Exception as e:
            self.logger.error(f"处理跳转到第一行快捷键失败: {e}")
    
    def _handle_goto_last_row_shortcut(self):
        """处理跳转到最后一行快捷键"""
        try:
            if self.rowCount() > 0:
                last_row = self.rowCount() - 1
                self.setCurrentCell(last_row, 0)
                self.scrollToBottom()
                self.logger.debug(f"跳转到最后一行: {last_row}")
            else:
                self.logger.debug("表格为空，无法跳转")
        except Exception as e:
            self.logger.error(f"处理跳转到最后一行快捷键失败: {e}")
    
    def _handle_refresh_data_shortcut(self):
        """处理刷新数据快捷键"""
        try:
            # 这里可以实现数据刷新逻辑
            # 比如重新从数据源加载数据
            self.logger.debug("刷新数据功能触发")
            
            # 可以发出信号通知外部组件刷新数据
            # self.refresh_requested.emit()
        except Exception as e:
            self.logger.error(f"处理刷新数据快捷键失败: {e}")
    
    def _handle_show_help_shortcut(self):
        """处理显示帮助快捷键"""
        try:
            help_text = self.shortcut_manager.get_shortcut_help_text()
            
            # 发出帮助请求信号
            self.shortcut_help_requested.emit()
            
            # 也可以直接显示帮助对话框
            self._show_shortcut_help_dialog(help_text)
            
        except Exception as e:
            self.logger.error(f"处理显示帮助快捷键失败: {e}")
    
    def _show_shortcut_help_dialog(self, help_text: str):
        """显示快捷键帮助对话框"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton
            
            dialog = QDialog(self)
            dialog.setWindowTitle("快捷键帮助")
            dialog.resize(500, 400)
            
            layout = QVBoxLayout(dialog)
            
            # 帮助文本
            text_edit = QTextEdit()
            text_edit.setPlainText(help_text)
            text_edit.setReadOnly(True)
            text_edit.setFont(QFont("Consolas", 10))
            layout.addWidget(text_edit)
            
            # 关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.close)
            layout.addWidget(close_button)
            
            dialog.exec_()
            
        except Exception as e:
            self.logger.error(f"显示快捷键帮助对话框失败: {e}")
    
    def _on_shortcut_help_requested(self, shortcuts_list: List[Dict[str, Any]]):
        """处理快捷键帮助请求"""
        try:
            self.shortcut_help_requested.emit()
            self.logger.debug("快捷键帮助请求已发出")
        except Exception as e:
            self.logger.error(f"处理快捷键帮助请求失败: {e}")
    
    def set_shortcuts_enabled(self, enabled: bool):
        """设置快捷键是否启用"""
        try:
            self.shortcuts_enabled = enabled
            
            # 也可以批量启用/禁用所有快捷键
            for shortcut_name in self.shortcut_manager.shortcuts:
                self.shortcut_manager.enable_shortcut(shortcut_name, enabled)
            
            self.logger.debug(f"快捷键系统设置为: {'启用' if enabled else '禁用'}")
        except Exception as e:
            self.logger.error(f"设置快捷键状态失败: {e}")
    
    def enable_shortcut(self, shortcut_name: str, enabled: bool = True):
        """启用或禁用指定快捷键"""
        try:
            return self.shortcut_manager.enable_shortcut(shortcut_name, enabled)
        except Exception as e:
            self.logger.error(f"设置指定快捷键状态失败: {shortcut_name}, 错误: {e}")
            return False
    
    def get_shortcuts_help_text(self) -> str:
        """获取快捷键帮助文本"""
        try:
            return self.shortcut_manager.get_shortcut_help_text()
        except Exception as e:
            self.logger.error(f"获取快捷键帮助文本失败: {e}")
            return "快捷键帮助获取失败"
    
    def get_shortcuts_by_category(self) -> Dict[str, List[Dict[str, Any]]]:
        """按分类获取快捷键列表"""
        try:
            return self.shortcut_manager.get_shortcuts_by_category()
        except Exception as e:
            self.logger.error(f"获取快捷键分类失败: {e}")
            return {}
    
    def get_shortcut_usage_statistics(self) -> Dict[str, Any]:
        """获取快捷键使用统计"""
        try:
            return self.shortcut_manager.get_usage_statistics()
        except Exception as e:
            self.logger.error(f"获取快捷键使用统计失败: {e}")
            return {}
    
    def detect_shortcut_conflicts(self) -> List[Dict[str, Any]]:
        """检测快捷键冲突"""
        return self.shortcut_manager.detect_conflicts()
    
    def show_shortcut_help(self):
        """显示快捷键帮助"""
        help_text = self.get_shortcuts_help_text()
        self._show_shortcut_help_dialog(help_text)
    
    # ==================== 拖拽排序功能 (P4-005) ====================
    
    def set_drag_sort_enabled(self, enabled: bool):
        """启用/禁用拖拽排序功能"""
        self.drag_sort_enabled = enabled
        self.drag_sort_manager.enable_drag_sort(enabled)
        
        if enabled:
            # 启用拖拽功能
            self.setDragEnabled(True)
            self.setAcceptDrops(True)
            self.setDropIndicatorShown(True)
            self.setDragDropMode(QAbstractItemView.InternalMove)
            self.setDefaultDropAction(Qt.MoveAction)
        else:
            # 禁用拖拽功能
            self.setDragEnabled(False)
            self.setAcceptDrops(False)
            self.setDropIndicatorShown(False)
            self.setDragDropMode(QAbstractItemView.NoDragDrop)
        
        self.logger.info(f"拖拽排序功能{'启用' if enabled else '禁用'}")
    
    def set_drag_constraints(self, 
                           cross_group: bool = True,
                           expanded_rows: bool = True,
                           allowed_rows: Optional[List[int]] = None):
        """设置拖拽约束条件"""
        self.drag_sort_manager.set_drag_constraints(cross_group, expanded_rows, allowed_rows)
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件 - 检测拖拽开始"""
        try:
            if event.button() == Qt.LeftButton and self.drag_sort_enabled:
                # 记录按下位置用于拖拽检测
                self.drag_start_position = event.pos()
                self.is_potential_drag = True
            
            # 调用父类处理
            super().mousePressEvent(event)
            
        except Exception as e:
            self.logger.error(f"鼠标按下事件处理失败: {e}")
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 处理拖拽"""
        try:
            if (self.is_potential_drag and 
                event.buttons() & Qt.LeftButton and 
                self.drag_sort_enabled):
                
                # 检查是否达到拖拽阈值
                distance = (event.pos() - self.drag_start_position).manhattanLength()
                if distance >= self.drag_sort_manager.drag_threshold:
                    self._start_drag_operation(event.pos())
            
            # 如果正在拖拽，更新拖拽状态
            if self.drag_sort_manager.is_dragging:
                current_row = self.rowAt(event.pos().y())
                self.drag_sort_manager.update_drag(current_row)
            
            # 调用父类处理
            super().mouseMoveEvent(event)
            
        except Exception as e:
            self.logger.error(f"鼠标移动事件处理失败: {e}")
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件 - 完成拖拽"""
        try:
            if event.button() == Qt.LeftButton:
                self.is_potential_drag = False
                
                # 如果正在拖拽，完成拖拽操作
                if self.drag_sort_manager.is_dragging:
                    self.drag_sort_manager.complete_drag()
            
            # 调用父类处理
            super().mouseReleaseEvent(event)
            
        except Exception as e:
            self.logger.error(f"鼠标释放事件处理失败: {e}")
    
    def startDrag(self, supportedActions):
        """开始拖拽操作 - PyQt5拖拽系统集成"""
        try:
            current_row = self.currentRow()
            if current_row < 0 or not self.drag_sort_enabled:
                return
            
            # 获取行数据
            row_data = self.model.get_row_data(current_row)
            if not row_data:
                return
            
            # 开始拖拽
            if self.drag_sort_manager.start_drag(current_row, self.drag_start_position, row_data.__dict__):
                # 创建拖拽对象
                drag = QDrag(self)
                mime_data = QMimeData()
                mime_data.setText(f"row:{current_row}")
                drag.setMimeData(mime_data)
                
                # 创建拖拽预览图
                pixmap = self.drag_sort_manager.create_drag_preview(self, current_row)
                drag.setPixmap(pixmap)
                drag.setHotSpot(QPoint(pixmap.width() // 2, pixmap.height() // 2))
                
                # 执行拖拽
                result = drag.exec_(Qt.MoveAction)
                
                # 处理拖拽结果
                if result != Qt.MoveAction:
                    self.drag_sort_manager.cancel_drag()
        
        except Exception as e:
            self.logger.error(f"开始拖拽操作失败: {e}")
            self.drag_sort_manager.cancel_drag()
    
    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if (event.mimeData().hasText() and 
            event.mimeData().text().startswith("row:") and
            self.drag_sort_enabled):
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def dragMoveEvent(self, event):
        """拖拽移动事件"""
        if (event.mimeData().hasText() and 
            event.mimeData().text().startswith("row:") and
            self.drag_sort_enabled):
            
            # 更新拖拽状态
            target_row = self.rowAt(event.pos().y())
            if self.drag_sort_manager.update_drag(target_row):
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    
    def dropEvent(self, event):
        """拖拽放置事件"""
        try:
            if not (event.mimeData().hasText() and 
                   event.mimeData().text().startswith("row:") and
                   self.drag_sort_enabled):
                event.ignore()
                return
            
            # 获取目标行
            target_row = self.rowAt(event.pos().y())
            
            # 完成拖拽操作
            if self.drag_sort_manager.is_dragging:
                self.drag_sort_manager.drag_target_row = target_row
                success = self.drag_sort_manager.complete_drag()
                
                if success:
                    event.acceptProposedAction()
                else:
                    event.ignore()
            else:
                event.ignore()
                
        except Exception as e:
            self.logger.error(f"拖拽放置事件处理失败: {e}")
            event.ignore()
    
    def _start_drag_operation(self, position: QPoint):
        """启动拖拽操作"""
        try:
            current_row = self.rowAt(position.y())
            if current_row < 0:
                return
            
            # 获取行数据
            row_data = self.model.get_row_data(current_row)
            if not row_data:
                return
            
            # 开始拖拽
            self.drag_sort_manager.start_drag(current_row, position, row_data.__dict__)
            self.is_potential_drag = False
            
        except Exception as e:
            self.logger.error(f"启动拖拽操作失败: {e}")
    
    def _move_row_data(self, from_row: int, to_row: int) -> bool:
        """移动行数据"""
        try:
            if (from_row < 0 or to_row < 0 or 
                from_row >= self.rowCount() or 
                to_row >= self.rowCount() or
                from_row == to_row):
                return False
            
            # 获取源行数据
            source_data = self.model.get_row_data(from_row)
            if not source_data:
                return False
            
            # 保存表格项数据
            row_items = []
            for col in range(self.columnCount()):
                item = self.item(from_row, col)
                if item:
                    row_items.append(item.clone())
                else:
                    row_items.append(None)
            
            # 调整目标行索引（如果目标行在源行之后）
            actual_target = to_row if to_row < from_row else to_row - 1
            
            # 删除源行
            self.removeRow(from_row)
            
            # 在目标位置插入新行
            self.insertRow(actual_target)
            
            # 恢复表格项数据
            for col, item in enumerate(row_items):
                if item:
                    self.setItem(actual_target, col, item)
            
            # 更新数据模型
            self.model.move_row_data(from_row, actual_target)
            
            # 选中移动后的行
            self.selectRow(actual_target)
            
            self.logger.info(f"行数据移动成功: {from_row} -> {actual_target}")
            return True
            
        except Exception as e:
            self.logger.error(f"移动行数据失败: {e}")
            return False
    
    # 拖拽事件处理回调
    def _on_drag_started(self, source_row: int):
        """拖拽开始回调"""
        self.logger.info(f"拖拽开始: 行 {source_row}")
        
        # 发射拖拽开始信号
        self.row_drag_started.emit(source_row)
        
        # 可以在这里添加视觉反馈
        self.setProperty("dragging", True)
        self.style().unpolish(self)
        self.style().polish(self)
    
    def _on_drag_moved(self, source_row: int, target_row: int):
        """拖拽移动回调"""
        self.logger.debug(f"拖拽移动: {source_row} -> {target_row}")
        
        # 可以在这里添加拖拽预览效果
        pass
    
    def _on_drag_completed(self, from_row: int, to_row: int, success: bool):
        """拖拽完成回调"""
        try:
            if success:
                # 执行实际的行移动
                move_success = self._move_row_data(from_row, to_row)
                
                if move_success:
                    self.logger.info(f"拖拽排序完成: {from_row} -> {to_row}")
                    
                    # 发射行重排序信号
                    self.row_reordered.emit(from_row, to_row)
                    
                    # 发射拖拽操作完成信号
                    self.drag_operation_completed.emit("row_reorder", True)
                    
                    # 更新选择状态
                    self._update_selection_after_move(from_row, to_row)
                else:
                    self.logger.warning(f"拖拽排序失败: 行移动操作失败")
                    self.drag_operation_completed.emit("row_reorder", False)
            else:
                self.logger.info(f"拖拽操作未完成")
                self.drag_operation_completed.emit("drag_cancelled", False)
                
        except Exception as e:
            self.logger.error(f"拖拽完成回调处理失败: {e}")
            self.drag_operation_completed.emit("error", False)
        finally:
            # 清理拖拽状态
            self.setProperty("dragging", False)
            self.style().unpolish(self)
            self.style().polish(self)
    
    def _on_drag_cancelled(self, source_row: int):
        """拖拽取消回调"""
        self.logger.info(f"拖拽取消: 行 {source_row}")
        
        # 清理拖拽状态
        self.setProperty("dragging", False)
        self.style().unpolish(self)
        self.style().polish(self)
    
    def _update_selection_after_move(self, from_row: int, to_row: int):
        """移动后更新选择状态"""
        try:
            # 计算实际目标位置
            actual_target = to_row if to_row < from_row else to_row - 1
            
            # 更新选择状态
            if from_row in self.selected_rows:
                self.selected_rows.remove(from_row)
                self.selected_rows.add(actual_target)
            
            # 选中移动后的行
            self.selectRow(actual_target)
            
            # 更新显示
            self._update_selection_display()
            
        except Exception as e:
            self.logger.error(f"更新选择状态失败: {e}")
    
    def get_drag_sort_statistics(self) -> Dict[str, Any]:
        """获取拖拽排序统计信息"""
        return self.drag_sort_manager.get_drag_statistics()
    
    def cancel_current_drag(self):
        """取消当前拖拽操作"""
        if self.drag_sort_manager.is_dragging:
            self.drag_sort_manager.cancel_drag()
    
    # ==================== 字段映射编辑功能 ====================
    
    def _on_header_double_clicked(self, logical_index: int):
        """处理表头双击事件 - 启动字段编辑"""
        try:
            if not self.header_edit_enabled:
                return
            
            # 获取当前字段名
            current_name = self.horizontalHeaderItem(logical_index)
            if current_name:
                current_name = current_name.text()
            else:
                current_name = f"Column_{logical_index}"
            
            # 显示编辑对话框
            success = self.header_edit_manager.show_edit_dialog(
                self, current_name, self.current_table_name, logical_index
            )
            
            if success:
                # 更新表头显示
                self._refresh_header_display()
                
                # 发出信号
                new_name = self._get_mapped_field_name(current_name)
                self.header_edited.emit(logical_index, current_name, new_name)
                
                # 发出配置更新信号
                mapping = self.config_sync_manager.load_mapping(self.current_table_name)
                if mapping:
                    self.field_mapping_updated.emit(self.current_table_name, mapping)
                
                self.logger.info(f"表头编辑成功: 列{logical_index}, {current_name} -> {new_name}")
            
        except Exception as e:
            self.logger.error(f"处理表头双击事件失败: 列{logical_index}, 错误: {e}")
    
    def set_table_name(self, table_name: str):
        """设置当前表名，用于字段映射"""
        try:
            self.current_table_name = table_name
            self.logger.debug(f"设置表名: {table_name}")
        except Exception as e:
            self.logger.error(f"设置表名失败: {e}")
    
    def set_header_edit_enabled(self, enabled: bool):
        """启用/禁用表头编辑功能"""
        try:
            self.header_edit_enabled = enabled
            self.logger.debug(f"表头编辑功能: {'启用' if enabled else '禁用'}")
        except Exception as e:
            self.logger.error(f"设置表头编辑状态失败: {e}")
    
    def _refresh_header_display(self):
        """刷新表头显示，应用最新的字段映射"""
        try:
            mapping = self.config_sync_manager.load_mapping(self.current_table_name)
            if not mapping:
                self.logger.debug(f"表 {self.current_table_name} 没有字段映射配置")
                return
            
            self.logger.debug(f"应用字段映射到表头: {mapping}")
            
            # 更新表头标签
            updated_count = 0
            for column in range(self.columnCount()):
                header_item = self.horizontalHeaderItem(column)
                if header_item:
                    original_name = header_item.text()
                    # 直接从映射字典查找显示名称
                    if original_name in mapping:
                        display_name = mapping[original_name]
                        if display_name != original_name:
                            header_item.setText(display_name)
                            updated_count += 1
                            self.logger.debug(f"表头更新: {original_name} -> {display_name}")
            
            self.logger.info(f"表头显示刷新完成，更新了 {updated_count} 个表头")
            
        except Exception as e:
            self.logger.error(f"刷新表头显示失败: {e}", exc_info=True)
    
    def _get_mapped_field_name(self, original_name: str) -> str:
        """获取字段的映射显示名称"""
        try:
            mapping = self.config_sync_manager.load_mapping(self.current_table_name)
            if mapping:
                return mapping.get(original_name, original_name)
            return original_name
        except Exception as e:
            self.logger.error(f"获取字段映射名称失败: {e}")
            return original_name
    
    def apply_field_mapping(self, mapping: Dict[str, str]):
        """应用字段映射到表头显示"""
        try:
            # 保存映射到配置管理器
            self.config_sync_manager.save_mapping(self.current_table_name, mapping)
            
            # 刷新表头显示
            self._refresh_header_display()
            
            # 发出信号
            self.field_mapping_updated.emit(self.current_table_name, mapping)
            
            self.logger.info(f"字段映射应用成功: {len(mapping)} 个字段")
            
        except Exception as e:
            self.logger.error(f"应用字段映射失败: {e}")
    
    def get_field_mapping_stats(self) -> Dict[str, Any]:
        """获取字段映射统计信息"""
        try:
            # 先获取基本的字段映射
            mapping = self.config_sync_manager.load_mapping(self.current_table_name)
            if not mapping:
                return {"error": "无映射配置"}
            
            # 尝试获取详细配置信息
            try:
                config = self.config_sync_manager._load_config_file()
                table_config = config.get("table_mappings", {}).get(self.current_table_name, {})
                metadata = table_config.get('metadata', {})
                edit_history = table_config.get('edit_history', [])
            except:
                metadata = {}
                edit_history = []
            
            return {
                "table_name": self.current_table_name,
                "total_fields": len(mapping),
                "mapped_fields": len([v for k, v in mapping.items() if k != v]),
                "auto_generated": metadata.get('auto_generated', False),
                "user_modified": metadata.get('user_modified', False),
                "last_modified": metadata.get('last_modified', ''),
                "edit_history_count": len(edit_history)
            }
            
        except Exception as e:
            self.logger.error(f"获取字段映射统计失败: {e}")
            return {"error": str(e)}


# 示例和测试代码
def create_sample_data(count: int = 100) -> Tuple[List[Dict[str, Any]], List[str]]:
    """
    创建示例数据
    
    Args:
        count: 数据行数
        
    Returns:
        Tuple[List[Dict[str, Any]], List[str]]: 数据和表头
    """
    headers = ["ID", "姓名", "部门", "职位", "薪资", "入职日期", "状态"]
    
    departments = ["技术部", "市场部", "财务部", "人事部", "运营部"]
    positions = ["工程师", "经理", "主管", "专员", "总监"]
    
    data = []
    for i in range(count):
        row = {
            "ID": f"EMP{i+1:04d}",
            "姓名": f"员工{i+1}",
            "部门": departments[i % len(departments)],
            "职位": positions[i % len(positions)],
            "薪资": 5000 + (i % 20) * 1000,
            "入职日期": f"2023-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}",
            "状态": "在职" if i % 10 != 0 else "离职"
        }
        data.append(row)
    
    return data, headers


def demo_max_visible_rows():
    """
    演示max_visible_rows功能的示例
    """
    import sys
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = QMainWindow()
    window.setWindowTitle("VirtualizedExpandableTable - max_visible_rows 演示")
    window.resize(1200, 800)
    
    # 创建表格组件，指定max_visible_rows
    table = VirtualizedExpandableTable(parent=window, max_visible_rows=500)
    window.setCentralWidget(table)
    
    # 创建大量测试数据
    large_data, headers = create_sample_data(2000)
    
    # 设置数据，启用自动调整
    table.set_data(large_data, headers, auto_adjust_visible_rows=True)
    
    # 输出性能统计
    stats = table.get_performance_stats()
    print(f"性能统计: {stats}")
    
    # 手动调整max_visible_rows
    print(f"当前最大可见行数: {table.get_max_visible_rows()}")
    
    # 尝试设置不同的值
    table.set_max_visible_rows(200)
    print(f"调整后最大可见行数: {table.get_max_visible_rows()}")
    
    # 尝试设置无效值
    success = table.set_max_visible_rows(-1)
    print(f"设置无效值(-1)是否成功: {success}")
    
    # 再次输出性能统计
    stats = table.get_performance_stats()
    print(f"调整后性能统计: {stats}")
    
    window.show()
    return app.exec_()


if __name__ == "__main__":
    # 运行演示
    demo_max_visible_rows() 