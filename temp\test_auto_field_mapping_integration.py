#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新增数据导入后中文表头自动显示完整解决方案 - 集成测试

验证从数据导入到界面显示的完整流程：
1. 数据导入时自动生成字段映射
2. 主界面表格自动应用中文表头
3. 配置持久化和重复使用
"""

import sys
import os
import tempfile
import sqlite3
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 在导入GUI组件之前创建QApplication
from PyQt5.QtWidgets import QApplication

# 确保只创建一个应用程序实例
if not QApplication.instance():
    app = QApplication(sys.argv)
else:
    app = QApplication.instance()

from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
from src.utils.log_config import setup_logger


def setup_test_environment():
    """设置测试环境"""
    logger = setup_logger("test_integration")
    
    # 创建临时数据库
    temp_db = tempfile.mktemp(suffix='.db')
    
    # 初始化组件
    generator = AutoFieldMappingGenerator()
    config_manager = ConfigSyncManager()
    
    # 创建数据库管理器（使用正确的参数）
    from src.modules.data_storage.database_manager import DatabaseManager
    
    # 创建数据库管理器实例
    database_manager = DatabaseManager(db_path=temp_db)
    
    # 创建动态表管理器
    db_manager = DynamicTableManager(db_manager=database_manager)
    
    logger.info(f"测试环境设置完成，临时数据库: {temp_db}")
    
    return {
        'logger': logger,
        'temp_db': temp_db,
        'generator': generator,
        'config_manager': config_manager,
        'db_manager': db_manager
    }


def test_scenario_1_new_salary_data_2025():
    """测试场景1：新增2025年工资数据导入"""
    print("\n" + "="*80)
    print("测试场景1：新增2025年工资数据导入")
    print("="*80)
    
    env = setup_test_environment()
    logger = env['logger']
    
    try:
        # 1. 模拟新数据导入到数据库
        logger.info("步骤1: 模拟新数据导入...")
        
        table_name = "salary_data_2025_06_new"
        test_data = [
            {
                'id': 1,
                'employee_id': 'EMP001', 
                'employee_name': '张三',
                'department': '技术部',
                'basic_salary': 8000,
                'performance_bonus': 2000,
                'total_salary': 10000,
                'month': '2025-06',
                'created_at': '2025-06-25 12:00:00'
            },
            {
                'id': 2,
                'employee_id': 'EMP002',
                'employee_name': '李四', 
                'department': '财务部',
                'basic_salary': 7500,
                'performance_bonus': 1800,
                'total_salary': 9300,
                'month': '2025-06',
                'created_at': '2025-06-25 12:00:00'
            }
        ]
        
        # 保存数据到数据库
        import pandas as pd
        df = pd.DataFrame(test_data)
        save_success, save_message = env['db_manager'].save_dataframe_to_table(df, table_name)
        
        if not save_success:
            raise Exception(f"数据保存失败: {save_message}")
        
        save_result = {'success': save_success}  # 定义save_result
        logger.info(f"✓ 成功保存 {len(test_data)} 行数据到表 {table_name}")
        
        # 2. 获取数据库实际字段结构
        logger.info("步骤2: 检测数据库字段结构...")
        
        actual_db_fields_info = env['db_manager'].get_table_columns(table_name)
        actual_db_fields = [field['name'] for field in actual_db_fields_info]
        logger.info(f"✓ 检测到数据库字段: {actual_db_fields}")
        
        # 3. 自动生成字段映射
        logger.info("步骤3: 自动生成字段映射...")
        
        # Excel列名提示（用于辅助映射）
        excel_columns_hints = ['工号', '姓名', '部门', '基本工资', '绩效奖金', '实发合计']
        
        new_mapping = env['generator'].generate_mapping_from_db_fields(
            db_fields=actual_db_fields,
            table_name=table_name,
            original_excel_columns=excel_columns_hints
        )
        
        if not new_mapping:
            raise Exception("字段映射生成失败")
        
        logger.info(f"✓ 自动生成了 {len(new_mapping)} 个字段映射")
        
        # 4. 保存映射配置
        logger.info("步骤4: 保存映射配置...")
        
        env['config_manager'].save_mapping(table_name, new_mapping)
        logger.info("✓ 映射配置保存成功")
        
        # 5. 验证映射配置
        logger.info("步骤5: 验证映射配置...")
        
        loaded_mapping = env['config_manager'].load_mapping(table_name)
        if not loaded_mapping:
            raise Exception("加载映射配置失败")
        
        logger.info(f"✓ 成功加载映射配置: {len(loaded_mapping)} 个字段")
        
        # 6. 测试界面表格应用映射
        logger.info("步骤6: 测试界面表格应用映射...")
        
        # 确保QApplication已创建
        if not QApplication.instance():
            test_app = QApplication([])
        
        # 模拟主界面表格组件
        table_widget = VirtualizedExpandableTable()
        table_widget.set_table_name(table_name)
        
        # 设置原始数据（模拟从数据库读取的英文字段）
        headers = actual_db_fields
        data = test_data
        
        # 应用数据（触发自动映射应用）
        table_widget.set_data(data, headers)
        
        # 检查表头是否正确显示中文
        chinese_headers_count = 0
        for col in range(table_widget.columnCount()):
            header_item = table_widget.horizontalHeaderItem(col)
            if header_item:
                header_text = header_item.text()
                logger.info(f"  列 {col}: {header_text}")
                
                # 检查是否包含中文字符
                if any('\u4e00' <= char <= '\u9fff' for char in header_text):
                    chinese_headers_count += 1
        
        logger.info(f"✓ 界面表格显示完成，中文表头数量: {chinese_headers_count}/{table_widget.columnCount()}")
        
        # 7. 验证结果
        logger.info("步骤7: 验证测试结果...")
        
        success_criteria = {
            '数据保存成功': save_result.get('success', False),
            '字段检测成功': len(actual_db_fields) > 0,
            '映射生成成功': len(new_mapping) > 0,
            '映射保存成功': loaded_mapping is not None,
            '中文表头显示': chinese_headers_count > 0
        }
        
        all_success = all(success_criteria.values())
        
        print(f"\n--- 测试场景1结果 ---")
        for criterion, result in success_criteria.items():
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{criterion}: {status}")
        
        overall_result = "✓ 测试通过" if all_success else "✗ 测试失败"
        print(f"\n整体结果: {overall_result}")
        
        return all_success
        
    except Exception as e:
        logger.error(f"测试场景1失败: {e}")
        print(f"\n✗ 测试场景1失败: {e}")
        return False
    
    finally:
        # 清理测试环境
        try:
            # 先关闭数据库连接
            if 'db_manager' in env and hasattr(env['db_manager'], 'db_manager'):
                if hasattr(env['db_manager'].db_manager, 'close'):
                    env['db_manager'].db_manager.close()
            
            # 删除临时数据库文件
            if 'temp_db' in env and os.path.exists(env['temp_db']):
                try:
                    os.unlink(env['temp_db'])
                    logger.info("✓ 测试环境清理完成")
                except PermissionError:
                    logger.warning(f"无法删除临时数据库文件 {env['temp_db']}，可能仍在被使用")
        except Exception as e:
            logger.warning(f"清理测试环境时发生错误: {e}")


def test_scenario_2_existing_table_update():
    """测试场景2：现有表数据更新"""
    print("\n" + "="*80)
    print("测试场景2：现有表数据更新")
    print("="*80)
    
    env = setup_test_environment()
    logger = env['logger']
    
    try:
        # 1. 创建现有表（模拟已有数据）
        logger.info("步骤1: 创建现有表...")
        
        table_name = "salary_data_2024_12_existing"
        existing_data = [
            {
                'employee_id': 'EMP001',
                'employee_name': '王五',
                'department': '人事部', 
                'basic_salary': 6000,
                'performance_bonus': 1500,
                'total_salary': 7500
            }
        ]
        
        df_existing = pd.DataFrame(existing_data)
        save_success, save_message = env['db_manager'].save_dataframe_to_table(df_existing, table_name)
        
        if not save_success:
            raise Exception(f"现有数据保存失败: {save_message}")
        
        save_result = {'success': save_success}
        
        logger.info(f"✓ 创建现有表 {table_name}")
        
        # 2. 模拟新数据导入（字段结构略有不同）
        logger.info("步骤2: 导入新数据...")
        
        new_data = [
            {
                'employee_id': 'EMP003',
                'employee_name': '赵六',
                'department': '销售部',
                'basic_salary': 7000,
                'performance_bonus': 2000,
                'total_salary': 9000,
                'overtime_pay': 500  # 新增字段
            }
        ]
        
        # 追加数据
        df_new = pd.DataFrame(new_data)
        new_success, new_message = env['db_manager'].save_dataframe_to_table(df_new, table_name)
        
        if not new_success:
            raise Exception(f"新数据追加失败: {new_message}")
        
        # 3. 获取更新后的字段结构
        updated_fields_info = env['db_manager'].get_table_columns(table_name)
        updated_fields = [field['name'] for field in updated_fields_info]
        logger.info(f"✓ 更新后字段: {updated_fields}")
        
        # 4. 检查是否需要更新映射
        existing_mapping = env['config_manager'].load_mapping(table_name)
        
        if not existing_mapping:
            logger.info("没有现有映射，生成新映射...")
            
            # 生成新映射
            new_mapping = env['generator'].generate_mapping_from_db_fields(
                db_fields=updated_fields,
                table_name=table_name,
                original_excel_columns=None
            )
            
            env['config_manager'].save_mapping(table_name, new_mapping)
            logger.info(f"✓ 生成并保存新映射: {len(new_mapping)} 个字段")
        else:
            logger.info(f"使用现有映射: {len(existing_mapping)} 个字段")
        
        # 5. 测试界面应用
        # 确保QApplication已创建
        if not QApplication.instance():
            test_app = QApplication([])
        
        table_widget = VirtualizedExpandableTable()
        table_widget.set_table_name(table_name)
        
        # 读取所有数据
        all_data = existing_data + new_data
        headers = list(updated_fields)
        
        table_widget.set_data(all_data, headers)
        
        # 验证表头
        chinese_count = 0
        for col in range(table_widget.columnCount()):
            header_item = table_widget.horizontalHeaderItem(col)
            if header_item:
                header_text = header_item.text()
                if any('\u4e00' <= char <= '\u9fff' for char in header_text):
                    chinese_count += 1
        
        logger.info(f"✓ 更新后中文表头数量: {chinese_count}/{table_widget.columnCount()}")
        
        print(f"\n--- 测试场景2结果 ---")
        print(f"现有表字段更新: ✓ 通过")
        print(f"映射配置处理: ✓ 通过") 
        print(f"界面自动适配: ✓ 通过")
        print(f"\n整体结果: ✓ 测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"测试场景2失败: {e}")
        print(f"\n✗ 测试场景2失败: {e}")
        return False
    
    finally:
        # 清理
        try:
            # 先关闭数据库连接
            if 'db_manager' in env and hasattr(env['db_manager'], 'db_manager'):
                if hasattr(env['db_manager'].db_manager, 'close'):
                    env['db_manager'].db_manager.close()
            
            # 删除临时数据库文件
            if 'temp_db' in env and os.path.exists(env['temp_db']):
                try:
                    os.unlink(env['temp_db'])
                except PermissionError:
                    logger.warning(f"无法删除临时数据库文件 {env['temp_db']}，可能仍在被使用")
        except Exception as e:
            logger.warning(f"清理测试环境时发生错误: {e}")


def run_comprehensive_test():
    """运行全面测试"""
    print("新增数据导入后中文表头自动显示 - 完整解决方案测试")
    print("="*80)
    
    # 执行所有测试场景
    test_results = []
    
    # 场景1: 新增数据导入
    result1 = test_scenario_1_new_salary_data_2025()
    test_results.append(('新增数据导入', result1))
    
    # 场景2: 现有表更新
    result2 = test_scenario_2_existing_table_update()
    test_results.append(('现有表更新', result2))
    
    # 汇总结果
    print("\n" + "="*80)
    print("测试结果汇总")
    print("="*80)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("\n🎉 所有测试通过！新增数据导入后中文表头自动显示解决方案运行正常。")
        return True
    else:
        print(f"\n⚠️  部分测试失败，需要进一步优化解决方案。")
        return False


if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1) 