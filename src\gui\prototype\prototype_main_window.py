"""
PyQt5重构高保真原型 - 原型主窗口

实现高保真HTML原型的完整界面重构，包括：
- Header + Workspace(Left Panel + Main Content) + Footer 三段式布局
- Material Design色彩体系和现代化视觉效果  
- 响应式设计支持1024px-1920px+屏幕尺寸
- 4级树形导航、表格行展开、双击编辑、查询筛选等交互
- 隐藏式菜单栏功能

主要功能:
1. 三段式响应式布局架构
2. Material Design风格Header(蓝绿渐变)
3. 智能左侧导航面板(集成SmartTreeExpansion和SmartSearchDebounce)
4. 主工作区域(控制面板+数据表格)
5. 底部状态栏和信息显示
6. 隐藏式菜单栏系统
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QFrame, QLabel, QPushButton, QLineEdit, QTreeWidget, QTableWidget,
    QTabWidget, QStatusBar, QMenuBar, QToolBar, QHeaderView,
    QTreeWidgetItem, QTableWidgetItem, QSizePolicy, QDialog, QApplication,
    QStackedWidget, QSpacerItem, QAction, QMenu, QMessageBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSize, QRect, pyqtSlot, QThreadPool, QRunnable, QObject, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QLinearGradient, QBrush
from typing import Optional, Dict, Any, List
import logging
import time
from pathlib import Path
import pandas as pd
import sys
import traceback
import re
from datetime import datetime

from src.utils.log_config import setup_logger
from .managers.responsive_layout_manager import ResponsiveLayoutManager
from .managers.communication_manager import ComponentCommunicationManager
from .widgets.enhanced_navigation_panel import EnhancedNavigationPanel
from .widgets.virtualized_expandable_table import (
    VirtualizedExpandableTable, create_sample_data
)
from .widgets.material_tab_widget import MaterialTabWidget
from src.gui.widgets.pagination_widget import PaginationWidget

# 导入新的核心架构组件
from src.core.application_state_service import get_application_state_service
from src.core.application_controller import get_application_controller

# 导入数据导入相关模块
from src.modules.data_import.excel_importer import ExcelImporter
from src.modules.data_import.data_validator import DataValidator
from src.gui.dialogs import DataImportDialog
from src.core.application_service import ApplicationService
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

# 导入项目内部模块
from src.utils.log_config import setup_logger
from src.modules.system_config import ConfigManager
from src.gui.widgets.pagination_cache_manager import PaginationCacheManager

# 表头管理器导入
from src.gui.table_header_manager import get_global_header_manager, TableHeaderManager

import os

from src.modules.system_config.user_preferences import UserPreferences

# 初始化日志记录器
logger = setup_logger("gui.prototype.prototype_main_window")


class WorkerSignals(QObject):
    """定义Worker线程可以发出的信号"""
    finished = pyqtSignal()
    error = pyqtSignal(tuple)
    result = pyqtSignal(object)
    window_resized = pyqtSignal(QSize)


class Worker(QRunnable):
    """通用工作线程，用于执行耗时操作而不阻塞UI线程"""
    def __init__(self, fn, *args, **kwargs):
        super(Worker, self).__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = WorkerSignals()

    @pyqtSlot()
    def run(self):
        """运行工作线程中的方法"""
        try:
            result = self.fn(*self.args, **self.kwargs)
            self.signals.result.emit(result)
        except Exception as e:
            logger.error(f"Worker线程执行失败: {e}", exc_info=True)
            self.signals.error.emit((type(e), e, e.__traceback__))
        finally:
            self.signals.finished.emit()


class PaginationWorker(QRunnable):
    """分页数据加载工作线程"""
    def __init__(self, table_manager, table_name, page=1, page_size=50, apply_mapping_func=None):
        super(PaginationWorker, self).__init__()
        self.table_manager = table_manager
        self.table_name = table_name
        self.page = page
        self.page_size = page_size
        self.apply_mapping_func = apply_mapping_func
        self.signals = WorkerSignals()
        self.logger = setup_logger(f"PaginationWorker.{table_name}")

    @pyqtSlot()
    def run(self):
        """加载分页数据"""
        try:
            self.logger.info(f"开始加载表 {self.table_name} 第{self.page}页数据，每页{self.page_size}条")
            
            # 使用分页查询
            df, total_records = self.table_manager.get_dataframe_paginated(
                self.table_name, self.page, self.page_size
            )
            
            if df is not None and not df.empty:
                # 应用字段映射
                if self.apply_mapping_func:
                    df = self.apply_mapping_func(df, self.table_name)
                
                self.logger.info(f"成功加载 {len(df)} 条数据，总记录数: {total_records}")
                
                result = {
                    'success': True,
                    'data': df,
                    'total_records': total_records,
                    'current_page': self.page,
                    'page_size': self.page_size,
                    'table_name': self.table_name
                }
            else:
                self.logger.info(f"表 {self.table_name} 无数据")
                result = {
                    'success': False,
                    'error': '数据表为空或不存在',
                    'table_name': self.table_name
                }
            
            self.signals.result.emit(result)
            
        except Exception as e:
            self.logger.error(f"加载分页数据失败: {e}", exc_info=True)
            result = {
                'success': False,
                'error': str(e),
                'table_name': self.table_name
            }
            self.signals.result.emit(result)
        finally:
            self.signals.finished.emit()


class MaterialHeaderWidget(QWidget):
    """
    Material Design风格Header组件
    
    实现蓝绿渐变背景、系统标题、用户信息等功能。
    """
    
    # 信号定义
    settings_clicked = pyqtSignal()  # 设置按钮点击信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(60)  # 默认高度，响应式时会调整
        self.settings_btn = None  # 设置按钮引用
        self.setup_ui()
        self.logger = setup_logger(__name__ + ".MaterialHeaderWidget")
    
    def setup_ui(self):
        """
        设置Header界面
        """
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(20)
        
        # 系统标题区域
        title_label = QLabel("月度工资异动处理系统")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: white; font-weight: bold;")
        
        # 版本信息
        version_label = QLabel("v2.0 高保真原型")
        version_label.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 12px;")
        
        # 用户信息区域
        user_info_label = QLabel("管理员")
        user_info_label.setStyleSheet("color: white; font-size: 14px;")
        
        # 设置按钮
        self.settings_btn = QPushButton("⚙️")
        self.settings_btn.setFixedSize(40, 40)
        self.settings_btn.setToolTip("点击显示/隐藏菜单栏")
        self.settings_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 20px;
                color: white;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)
        
        # 连接设置按钮点击事件
        self.settings_btn.clicked.connect(self.settings_clicked.emit)
        
        # 布局安排
        layout.addWidget(title_label)
        layout.addWidget(version_label)
        layout.addStretch()
        layout.addWidget(user_info_label)
        layout.addWidget(self.settings_btn)
    
    def update_settings_button_state(self, menu_visible: bool):
        """
        更新设置按钮状态
        
        Args:
            menu_visible: 菜单栏是否可见
        """
        if self.settings_btn:
            if menu_visible:
                self.settings_btn.setToolTip("点击隐藏菜单栏")
                self.settings_btn.setStyleSheet("""
                    QPushButton {
                        background-color: rgba(255, 255, 255, 0.4);
                        border: none;
                        border-radius: 20px;
                        color: white;
                        font-size: 16px;
                    }
                    QPushButton:hover {
                        background-color: rgba(255, 255, 255, 0.5);
                    }
                    QPushButton:pressed {
                        background-color: rgba(255, 255, 255, 0.3);
                    }
                """)
            else:
                self.settings_btn.setToolTip("点击显示菜单栏")
                self.settings_btn.setStyleSheet("""
                    QPushButton {
                        background-color: rgba(255, 255, 255, 0.2);
                        border: none;
                        border-radius: 20px;
                        color: white;
                        font-size: 16px;
                    }
                    QPushButton:hover {
                        background-color: rgba(255, 255, 255, 0.3);
                    }
                    QPushButton:pressed {
                        background-color: rgba(255, 255, 255, 0.1);
                    }
                """)
    
    def paintEvent(self, event):
        """
        绘制蓝绿渐变背景
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建蓝绿渐变
        gradient = QLinearGradient(0, 0, self.width(), 0)
        gradient.setColorAt(0, QColor(33, 150, 243))    # Material Blue
        gradient.setColorAt(1, QColor(0, 150, 136))     # Material Teal
        
        painter.fillRect(self.rect(), QBrush(gradient))
        painter.end()
    
    def handle_responsive_change(self, breakpoint: str, config: Dict[str, Any]):
        """
        处理响应式变化
        
        Args:
            breakpoint: 断点名称
            config: 布局配置
        """
        header_height = config.get('header_height', 60)
        self.setFixedHeight(header_height)
        
        # 字体缩放
        font_scale = config.get('font_scale', 1.0)
        for label in self.findChildren(QLabel):
            font = label.font()
            if "16" in label.styleSheet():  # 标题
                font.setPointSize(int(16 * font_scale))
            elif "12" in label.styleSheet():  # 版本
                font.setPointSize(int(12 * font_scale))
            elif "14" in label.styleSheet():  # 用户信息
                font.setPointSize(int(14 * font_scale))
            label.setFont(font)


class MainWorkspaceArea(QWidget):
    """
    主工作区域
    
    包含控制面板、标签导航、数据表格等核心功能区域。
    """
    
    # 添加信号定义
    import_requested = pyqtSignal()  # 导入数据请求信号
    export_requested = pyqtSignal()  # 导出报告请求信号
    add_record_requested = pyqtSignal()  # 添加记录请求信号
    refresh_requested = pyqtSignal()  # 刷新数据请求信号
    
    def __init__(self, table_manager: DynamicTableManager, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)

        self.table_manager = table_manager
        self.pagination_cache = PaginationCacheManager(max_cache_entries=10, max_memory_mb=5, ttl_seconds=300)
        self.current_page = 1
        self.current_page_size = 50
        self.total_records = 0
        self.current_table_name = ""
        
        self.setup_ui()

        # 获取并注册表头管理器
        self.header_manager = get_global_header_manager()
        if hasattr(self, 'table') and self.table:
            self.header_manager.register_table("main_table", self.table)
            self.logger.info("主表格已成功注册到表头管理器")

        self._connect_control_panel_signals()
        
        self.threadpool = QThreadPool()
        self.logger.info("主工作区初始化完成，线程池已准备就绪。")
        self.logger.info(f"线程池最大线程数: {self.threadpool.maxThreadCount()}")

        # 初始时显示提示信息
        self._initialize_table_data()

    def _connect_control_panel_signals(self):
        """连接控制面板按钮信号"""
        try:
            # 连接主控制按钮
            self.btn_add.clicked.connect(self._on_add_record)
            self.btn_import.clicked.connect(self._on_import_data)
            self.btn_export.clicked.connect(self._on_export_report)
            self.btn_refresh.clicked.connect(self._on_refresh_data)
            
            # 连接搜索按钮
            self.btn_search.clicked.connect(self._on_search)
            
            self.logger.info("控制面板按钮信号连接完成")
            
        except Exception as e:
            self.logger.error(f"连接控制面板信号失败: {e}")
    
    def _on_add_record(self):
        """处理添加记录按钮点击"""
        try:
            self.logger.info("添加记录功能被触发")
            self.add_record_requested.emit()
            # TODO: 实现添加记录对话框
            self._show_info_message("添加记录功能", "添加新的工资异动记录功能正在开发中...")
        except Exception as e:
            self.logger.error(f"添加记录处理失败: {e}")
    
    def _on_import_data(self):
        """处理导入数据按钮点击，仅发出信号，由主窗口处理。"""
        self.logger.info("数据导入功能被触发，发出 import_requested 信号。")
        self.import_requested.emit()
    
    def _on_export_report(self):
        """处理导出报告按钮点击"""
        try:
            self.logger.info("导出报告功能被触发")
            self.export_requested.emit()
            
            if not self.has_real_data:
                self._show_warning_message("导出提醒", "当前为示例数据，建议先导入真实数据后再导出报告")
                return
            
            # TODO: 实现报告导出功能
            self._show_info_message("导出报告功能", "报告导出功能正在开发中...")
            
        except Exception as e:
            self.logger.error(f"导出报告处理失败: {e}")
            self._show_error_message("导出失败", f"导出报告失败: {e}")
    
    def _on_refresh_data(self):
        """
        刷新数据，包括强制刷新导航面板和重新加载当前视图。
        """
        self.logger.info("刷新数据功能被触发，将强制刷新导航面板。")
        try:
            # 1. 强制刷新导航面板
            if hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'force_refresh_salary_data'):
                self.navigation_panel.force_refresh_salary_data()
                self.logger.info("导航面板已强制刷新。")

                # 2. 刷新当前视图 (可选，因为导航刷新后可能会自动选择默认项)
                current_path = self.navigation_panel.get_current_path()
                if current_path:
                    self.logger.info(f"刷新当前导航路径视图: {current_path}")
                    self._on_navigation_changed(current_path, {'path': current_path})
                
                self._show_info_message("刷新完成", "导航和数据已刷新。")
            else:
                self.logger.warning("导航面板不支持强制刷新。")
                self._show_warning_message("刷新失败", "当前组件不支持导航刷新。")
                
        except Exception as e:
            self.logger.error(f"刷新数据时发生错误: {e}", exc_info=True)
            self._show_error_message("刷新错误", f"刷新过程中发生错误: {e}")
    
    def _on_search(self):
        """处理搜索按钮点击"""
        try:
            search_text = self.search_input.text().strip()
            if not search_text:
                self._show_warning_message("搜索提醒", "请输入搜索关键词")
                return
            
            self.logger.info(f"搜索功能被触发，关键词: {search_text}")
            # TODO: 实现搜索功能
            self._show_info_message("搜索功能", f"搜索功能正在开发中...\n搜索关键词: {search_text}")
            
        except Exception as e:
            self.logger.error(f"搜索处理失败: {e}")
    
    def set_data(self, df: Optional[pd.DataFrame], preserve_headers: bool = False, table_name: str = ""):
        """公共接口：设置表格数据（支持分页模式）。"""
        if df is None or df.empty:
            self.logger.warning("尝试设置空数据，将显示提示信息。")
            self._show_empty_table_with_prompt()
            self.has_real_data = False
            # 重置分页状态
            if self.pagination_widget:
                self.pagination_widget.reset()
        else:
            self.logger.info(f"通过公共接口设置新的表格数据: {len(df)} 行")
            self.current_table_name = table_name
            
            if self.use_pagination and len(df) > 100:  # 当数据量大于100条时启用分页
                self._set_paginated_data(df, preserve_headers)
            else:
                # 直接显示所有数据
                headers = df.columns.tolist()
                data = df.to_dict('records')
                if preserve_headers:
                    self.current_headers = headers
                self.expandable_table.set_data(data, headers)
                
                # 设置表格组件的表名，用于字段映射功能
                if table_name:
                    self.expandable_table.set_table_name(table_name)
                
                # 更新分页组件状态
                if self.pagination_widget:
                    self.pagination_widget.set_total_records(len(df))
                    self.pagination_widget.set_current_page(1)
            
            self.has_real_data = True
    
    def _set_paginated_data(self, df: pd.DataFrame, preserve_headers: bool = False):
        """设置分页数据"""
        try:
            # 更新分页组件
            total_records = len(df)
            if self.pagination_widget:
                self.pagination_widget.set_total_records(total_records)
                state = self.pagination_widget.get_current_state()
                
                # 获取当前页数据
                start_idx = (state.current_page - 1) * state.page_size
                end_idx = start_idx + state.page_size
                page_df = df.iloc[start_idx:end_idx]
                
                # 设置表格数据
                headers = page_df.columns.tolist()
                data = page_df.to_dict('records')
                if preserve_headers:
                    self.current_headers = headers
                self.expandable_table.set_data(data, headers)
                
                # 设置表格组件的表名，用于字段映射功能
                if self.current_table_name:
                    self.expandable_table.set_table_name(self.current_table_name)
                
                self.logger.info(f"分页数据设置完成: 第{state.current_page}页, 显示{len(page_df)}条记录")
        except Exception as e:
            self.logger.error(f"设置分页数据失败: {e}")
    
    def _on_page_changed(self, page: int):
        """处理页码变化事件 - 通过主窗口重新加载数据"""
        try:
            if not self.current_table_name:
                self.logger.warning("没有当前表名，无法进行分页查询")
                return
            
            # 获取分页状态
            state = self.pagination_widget.get_current_state()
            
            # 通过主窗口重新加载分页数据
            main_window = self.parent()
            if hasattr(main_window, '_load_data_with_pagination'):
                main_window._load_data_with_pagination(
                    table_name=self.current_table_name,
                    page=page,
                    page_size=state.page_size
                )
                self.logger.info(f"请求加载第{page}页数据")
            else:
                self.logger.warning("主窗口不支持分页数据加载，回退到本地加载")
                # 回退到本地加载逻辑
                df, total_count = self.table_manager.get_dataframe_paginated(
                    self.current_table_name, page, state.page_size
                )
                
                if df is not None:
                    # 设置表格数据
                    headers = df.columns.tolist()
                    data = df.to_dict('records')
                    self.expandable_table.set_data(data, headers)
                    
                    # 设置表格组件的表名，用于字段映射功能
                    if self.current_table_name:
                        self.expandable_table.set_table_name(self.current_table_name)
                    
                    # 更新总记录数（防止数据变化）
                    if total_count != state.total_records:
                        self.pagination_widget.set_total_records(total_count)
                    
                    self.logger.info(f"本地页码变化处理完成: 第{page}页, 显示{len(df)}条记录")
                else:
                    self.logger.error("获取分页数据失败")
                    self._show_empty_table_with_prompt()
                
        except Exception as e:
            self.logger.error(f"处理页码变化失败: {e}")
    
    def _on_page_size_changed(self, page_size: int):
        """处理页面大小变化事件 - 重置到第一页"""
        try:
            if not self.current_table_name:
                self.logger.warning("没有当前表名，无法进行分页查询")
                return
            
            # 通过主窗口重新加载第一页数据（页面大小变化时重置到第一页）
            main_window = self.parent()
            if hasattr(main_window, '_load_data_with_pagination'):
                main_window._load_data_with_pagination(
                    table_name=self.current_table_name,
                    page=1,  # 重置到第一页
                    page_size=page_size
                )
                self.logger.info(f"页面大小变化，重新加载第1页，每页{page_size}条")
            else:
                # 回退到本地处理
                self._on_page_changed(1)  # 重置到第一页
                self.logger.info(f"本地页面大小变化处理完成: 每页{page_size}条")
            
        except Exception as e:
            self.logger.error(f"处理页面大小变化失败: {e}")
    
    def _on_pagination_refresh(self):
        """处理分页刷新事件 - 重新加载当前页"""
        try:
            if not self.current_table_name:
                self.logger.warning("没有当前表名，无法刷新数据")
                return
            
            # 通过主窗口重新加载当前页数据
            state = self.pagination_widget.get_current_state()
            main_window = self.parent()
            if hasattr(main_window, '_load_data_with_pagination'):
                main_window._load_data_with_pagination(
                    table_name=self.current_table_name,
                    page=state.current_page,
                    page_size=state.page_size
                )
                self.logger.info(f"请求刷新第{state.current_page}页数据")
            else:
                # 回退到本地处理
                self._on_page_changed(state.current_page)
                self.logger.info("本地分页数据刷新完成")
            
        except Exception as e:
            self.logger.error(f"刷新分页数据失败: {e}")
    
    def _apply_field_mapping_to_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """应用字段映射到DataFrame（简化版本，实际实现可能在主窗口中）"""
        # 这是一个简化的实现，实际应该从主窗口的方法中调用
        # 暂时直接返回原数据
        return df
    
    def _load_real_data(self):
        """从真实数据源加载数据(示例)"""
        try:
            # 数据文件路径
            data_dir = Path("data/工资表")
            if not data_dir.exists():
                return False
            
            # 查找Excel文件
            excel_files = list(data_dir.glob("*.xls*"))
            if not excel_files:
                return False
            
            # 选择最新的文件
            latest_file = max(excel_files, key=lambda f: f.stat().st_mtime)
            
            self.logger.info(f"尝试加载数据文件: {latest_file}")
            
            # 使用Excel导入器
            excel_importer = ExcelImporter()
            
            # 获取工作表
            sheet_names = excel_importer.get_sheet_names(str(latest_file))
            target_sheet = None
            
            # 优先选择"全部在职人员工资表"
            for sheet in sheet_names:
                if "全部在职人员" in sheet or "在职人员" in sheet:
                    target_sheet = sheet
                    break
            
            if not target_sheet and sheet_names:
                target_sheet = sheet_names[0]
            
            if not target_sheet:
                return False
            
            # 导入数据（启用过滤功能）
            df = excel_importer.import_data(
                file_path=str(latest_file),
                sheet_name=target_sheet,
                filter_invalid=True  # 启用数据过滤
            )
            
            if df.empty:
                self.logger.warning("Excel文件数据为空或全部记录被过滤")
                return False
            
            # 设置表格数据
            self.expandable_table.set_data(df)
            
            # 更新状态
            self.has_real_data = True
            self.current_data_source = f"Excel文件: {latest_file.name}"
            self.current_data_path = f"工资表>2025年>5月>{target_sheet}"
            
            # 尝试将数据保存到数据库以供后续使用
            self._save_to_database(df, ["工资表", "2025", "05", "全部在职人员"])
            
            self._update_status_label(f"已加载Excel数据: {len(df)}条记录", "success")
            
            self.logger.info(f"Excel数据加载成功: {len(df)}条记录，来源: {latest_file.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"真实数据加载失败: {e}")
            return False
    
    def _load_sample_data(self):
        """加载示例数据"""
        try:
            data, headers = create_sample_data(50)  # 减少示例数据量
            self.expandable_table.set_data(data)
            self.current_data = data
            self.current_headers = headers
            self.has_real_data = False
            
            self._update_status_label("示例数据模式 - 请导入Excel文件加载真实数据", "warning")
            
        except Exception as e:
            self.logger.error(f"加载示例数据失败: {e}")
    
    def _update_status_label(self, message: str, status_type: str = "info"):
        """更新状态标签"""
        try:
            if hasattr(self, 'status_label'):
                self.status_label.setText(message)
                
                # 根据状态类型设置样式
                if status_type == "success":
                    style = """
                        QLabel {
                            color: #4CAF50;
                            font-weight: bold;
                            padding: 8px;
                            background-color: #E8F5E8;
                            border-radius: 4px;
                        }
                    """
                elif status_type == "warning":
                    style = """
                        QLabel {
                            color: #FF9800;
                            font-weight: bold;
                            padding: 8px;
                            background-color: #FFF3E0;
                            border-radius: 4px;
                        }
                    """
                elif status_type == "error":
                    style = """
                        QLabel {
                            color: #F44336;
                            font-weight: bold;
                            padding: 8px;
                            background-color: #FFEBEE;
                            border-radius: 4px;
                        }
                    """
                else:  # info
                    style = """
                        QLabel {
                            color: #2196F3;
                            font-weight: bold;
                            padding: 8px;
                            background-color: #E3F2FD;
                            border-radius: 4px;
                        }
                    """
                
                self.status_label.setStyleSheet(style)
                
        except Exception as e:
            self.logger.error(f"更新状态标签失败: {e}")
    
    def _show_info_message(self, title: str, message: str):
        """显示信息消息"""
        QMessageBox.information(self, title, message)
    
    def _show_warning_message(self, title: str, message: str):
        """显示警告消息"""
        QMessageBox.warning(self, title, message)
    
    def _show_error_message(self, title: str, message: str):
        """显示错误消息"""
        QMessageBox.critical(self, title, message)
    
    def setup_ui(self):
        """
        设置工作区域界面
        """
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 控制面板区域
        self.control_panel = self.create_control_panel()
        layout.addWidget(self.control_panel)

        # Material Design标签导航栏
        self.tab_widget = MaterialTabWidget()

        # 创建标签页
        self.create_tabs()
        layout.addWidget(self.tab_widget)

        # 状态标签
        self.status_label = QLabel("系统就绪")
        self.status_label.setStyleSheet("color: #2196F3; font-size: 12px; padding: 5px;")
        layout.addWidget(self.status_label)
    
    def create_control_panel(self) -> QWidget:
        """
        创建控制面板
        
        Returns:
            控制面板部件
        """
        panel = QFrame()
        panel.setObjectName("control_panel")  # 添加对象名称用于样式管理
        panel.setFixedHeight(80)
        panel.setStyleSheet("""
            QFrame#control_panel {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
            }
        """)
        
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(15)
        
        # 操作按钮组
        self.btn_add = QPushButton("+ 新增异动")
        self.btn_import = QPushButton("📥 导入数据")
        self.btn_export = QPushButton("📤 导出报告")
        self.btn_refresh = QPushButton("🔄 刷新数据")
        
        # 设置按钮的响应式类名
        for btn in [self.btn_add, self.btn_import, self.btn_export, self.btn_refresh]:
            btn.setObjectName("control_button")
            btn.setProperty("button_size", "medium")  # 默认中等大小
        
        # 按钮基础样式
        btn_style = """
            QPushButton#control_button {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 500;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton#control_button:hover {
                background-color: #1976D2;
            }
            QPushButton#control_button:pressed {
                background-color: #1565C0;
            }
            QPushButton#control_button[button_size="small"] {
                padding: 8px 16px;
                font-size: 12px;
                min-width: 80px;
            }
            QPushButton#control_button[button_size="large"] {
                padding: 12px 24px;
                font-size: 16px;
                min-width: 120px;
            }
        """
        
        for btn in [self.btn_add, self.btn_import, self.btn_export, self.btn_refresh]:
            btn.setStyleSheet(btn_style)
        
        # 搜索区域
        search_container = QWidget()
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(10)
        
        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setObjectName("search_input")
        self.search_input.setPlaceholderText("搜索员工姓名、部门...")
        self.search_input.setProperty("font_scale", 1.0)  # 用于响应式字体缩放
        
        search_style = """
            QLineEdit#search_input {
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
                min-width: 200px;
            }
        """
        self.search_input.setStyleSheet(search_style)
        
        # 搜索按钮
        self.btn_search = QPushButton("🔍")
        self.btn_search.setObjectName("icon_button")
        self.btn_search.setFixedSize(40, 40)
        self.btn_search.setProperty("button_size", "medium")
        
        icon_btn_style = """
            QPushButton#icon_button {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 20px;
                font-size: 16px;
            }
            QPushButton#icon_button:hover {
                background-color: #5a6268;
            }
            QPushButton#icon_button[button_size="small"] {
                font-size: 14px;
            }
            QPushButton#icon_button[button_size="large"] {
                font-size: 18px;
            }
        """
        self.btn_search.setStyleSheet(icon_btn_style)
        
        # 布局安排 - 左侧操作按钮，右侧搜索
        layout.addWidget(self.btn_add)
        layout.addWidget(self.btn_import)
        layout.addWidget(self.btn_export)
        layout.addWidget(self.btn_refresh)
        layout.addStretch()  # 弹性空间
        
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.btn_search)
        layout.addWidget(search_container)
        
        # 存储控件引用用于响应式调整
        self.control_panel_widgets = {
            'panel': panel,
            'buttons': [self.btn_add, self.btn_import, self.btn_export, self.btn_refresh],
            'search_input': self.search_input,
            'icon_button': self.btn_search
        }
        
        return panel
    
    def create_tabs(self):
        """创建选项卡"""
        self.tabs = MaterialTabWidget()
        self.tabs.currentChanged.connect(self._on_material_tab_changed)

        # 创建选项卡内容
        self.overview_tab = self.create_overview_tab()
        self.detail_tab = self.create_detail_tab()
        self.report_tab = self.create_report_tab()

        # 添加选项卡
        self.tabs.addTab(self.overview_tab, "数据概览")
        self.tabs.addTab(self.detail_tab, "详细信息")
        self.tabs.addTab(self.report_tab, "分析报告")
    
    def create_overview_tab(self) -> QWidget:
        """创建数据概览选项卡"""
        overview_widget = QWidget()
        main_layout = QVBoxLayout(overview_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)

        # 主内容区域
        self.table_layout = QVBoxLayout()
        
        # 表格区域
        self.table_placeholder = QLabel("正在加载表格...")
        self.table_placeholder.setAlignment(Qt.AlignCenter)
        self.table_placeholder.setStyleSheet("font-size: 16px; color: #9E9E9E;")

        # 将占位符添加到布局中
        self.table_layout.addWidget(self.table_placeholder)
        self.table_widget = None # 实际表格将在数据加载时创建

        # 分页组件
        self.pagination_widget = PaginationWidget()
        self.pagination_widget.page_changed.connect(self._on_page_changed)
        self.pagination_widget.page_size_changed.connect(self._on_page_size_changed)
        self.pagination_widget.refresh_requested.connect(self._on_pagination_refresh)
        
        # 将表格和分页组件添加到主布局
        main_layout.addLayout(self.table_layout)
        main_layout.addWidget(self.pagination_widget, 0, Qt.AlignCenter)

        return overview_widget
    
    def _initialize_table_data(self):
        """初始化表格，显示欢迎或提示信息"""
        # 确保在主线程中执行UI操作
        QTimer.singleShot(0, self._show_empty_table_with_prompt)
    
    def _show_empty_table_with_prompt(self):
        """显示带有提示的空表格"""
        try:
            if self.table_widget is None:
                self.logger.info("首次创建表格实例")
                self.table_widget = VirtualizedExpandableTable(
                    parent=self,
                    max_visible_rows=1000,
                    dynamic_table_manager=self.table_manager
                )
                self.table_layout.replaceWidget(self.table_placeholder, self.table_widget)
                self.table_placeholder.deleteLater()
            
            self.logger.info("表格已初始化为空白状态，等待用户导入数据")
            self.table_widget.set_data([], [])
            self.status_label.setText('请通过"数据操作"->"导入数据"开始使用。')
            self.pagination_widget.reset()
        except Exception as e:
            self.logger.error(f"显示空表格提示时出错: {e}", exc_info=True)
    
    def _refresh_table_data(self):
        """刷新当前表格数据"""
        if self.current_table_name:
            self.logger.info(f"刷新表格数据: {self.current_table_name}, 第{self.current_page}页")
            # 重新加载当前页
            self._on_page_changed(self.current_page)
        else:
            self._show_empty_table_with_prompt()
            self.logger.info("没有当前表格，无法刷新")
    
    def _on_row_expanded(self, row: int, detail_data: Dict[str, Any]):
        """处理行展开事件"""
        try:
            self.status_label.setText(f"行 {row+1} 已展开 - 显示详细信息")
            # 这里可以添加更多的展开处理逻辑
            # 比如更新右侧面板、发送通信信号等
            
        except Exception as e:
            print(f"处理行展开事件失败: {e}")
    
    def _on_row_collapsed(self, row: int, main_data: Dict[str, Any]):
        """处理行折叠事件"""
        try:
            self.status_label.setText(f"行 {row+1} 已折叠")
            # 这里可以添加更多的折叠处理逻辑
            
        except Exception as e:
            print(f"处理行折叠事件失败: {e}")
    
    def _on_table_selection_changed(self, selected_rows: List[int]):
        """处理表格选择变化"""
        try:
            if selected_rows:
                self.status_label.setText(f"已选择 {len(selected_rows)} 行")
            else:
                self.status_label.setText("无选择")
                
        except Exception as e:
            print(f"处理表格选择变化失败: {e}")
    
    def create_detail_tab(self) -> QWidget:
        """
        创建异动详情标签页
        
        Returns:
            详情标签页部件
        """
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 详情表格（支持行展开）
        table = QTableWidget(15, 8)
        table.setHorizontalHeaderLabels([
            "类型", "姓名", "工号", "变更前", "变更后", "变更金额", "日期", "备注"
        ])
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #2196F3;
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(table)
        return widget
    
    def create_report_tab(self) -> QWidget:
        """
        创建报告预览标签页
        
        Returns:
            报告标签页部件
        """
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 报告预览区域
        preview_label = QLabel("报告预览区域\n\n点击'生成报告'按钮查看详细报告...")
        preview_label.setAlignment(Qt.AlignCenter)
        preview_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px dashed #ddd;
                color: #666;
                font-size: 16px;
                padding: 50px;
            }
        """)
        
        layout.addWidget(preview_label)
        return widget
    
    def _on_material_tab_changed(self, index: int):
        """处理Material Design标签页切换"""
        try:
            tab_names = ["数据概览", "异动详情", "报告预览"]
            if 0 <= index < len(tab_names):
                print(f"Material标签切换到: {tab_names[index]} (索引: {index})")
                # 这里可以添加更多的标签切换处理逻辑
                
        except Exception as e:
            print(f"处理Material标签切换失败: {e}")
    
    def handle_responsive_change(self, breakpoint: str, config: Dict[str, Any]):
        """
        处理响应式变化
        
        Args:
            breakpoint: 断点名称
            config: 布局配置
        """
        self.logger.info(f"MainWorkspaceArea 响应式适配: {breakpoint}")
        
        # 1. 控制面板响应式优化
        self._apply_control_panel_responsive(breakpoint, config)
        
        # 2. 标签导航响应式适配
        if hasattr(self.tab_widget, 'handle_responsive_change'):
            self.tab_widget.handle_responsive_change(breakpoint, config)
        
        # 3. 表格组件响应式适配
        if hasattr(self, 'table_widget'):
            self._apply_table_responsive(breakpoint, config)
    
    def _apply_control_panel_responsive(self, breakpoint: str, config: Dict[str, Any]):
        """
        应用控制面板的响应式配置
        
        Args:
            breakpoint: 当前断点
            config: 响应式配置
        """
        if not hasattr(self, 'control_panel_widgets'):
            return
        
        # 获取配置参数
        button_size = config.get('button_size', 'medium')
        font_scale = config.get('font_scale', 1.0)
        padding_scale = config.get('padding_scale', 1.0)
        
        # 控制面板高度调整
        panel = self.control_panel_widgets['panel']
        base_height = 80
        new_height = int(base_height * padding_scale)
        panel.setFixedHeight(new_height)
        
        # 按钮大小响应式调整
        buttons = self.control_panel_widgets['buttons']
        for btn in buttons:
            btn.setProperty("button_size", button_size)
            # 刷新样式
            btn.style().unpolish(btn)
            btn.style().polish(btn)
        
        # 搜索输入框响应式调整
        search_input = self.control_panel_widgets['search_input']
        search_input.setProperty("font_scale", font_scale)
        
        # 根据断点调整搜索框的最小宽度
        search_widths = {
            'xs': 150,   # 超小屏幕，紧凑搜索框
            'sm': 180,   # 小屏幕
            'md': 200,   # 中等屏幕，默认宽度
            'lg': 250,   # 大屏幕，更宽的搜索框
            'xl': 300    # 超大屏幕，最宽搜索框
        }
        search_width = search_widths.get(breakpoint, 200)
        
        # 动态更新搜索框样式
        search_style = f"""
            QLineEdit#search_input {{
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: {int(8 * padding_scale)}px {int(12 * padding_scale)}px;
                font-size: {int(14 * font_scale)}px;
                background-color: white;
                min-width: {search_width}px;
            }}
        """
        search_input.setStyleSheet(search_style)
        
        # 图标按钮响应式调整
        icon_button = self.control_panel_widgets['icon_button']
        icon_button.setProperty("button_size", button_size)
        icon_button.style().unpolish(icon_button)
        icon_button.style().polish(icon_button)
        
        # xs断点下的特殊处理
        if breakpoint == 'xs':
            # 超小屏幕下隐藏部分按钮，只保留核心功能
            self.btn_export.setVisible(False)
            self.btn_refresh.setVisible(False)
            # 搜索框占位符简化
            search_input.setPlaceholderText("搜索...")
        else:
            # 其他断点下显示所有按钮
            self.btn_export.setVisible(True)
            self.btn_refresh.setVisible(True)
            search_input.setPlaceholderText("搜索员工姓名、部门...")
        
        # 布局间距调整
        if hasattr(self.control_panel, 'layout'):
            layout = self.control_panel.layout()
            if layout:
                base_spacing = 15
                new_spacing = int(base_spacing * padding_scale)
                layout.setSpacing(new_spacing)
                
                # 边距调整
                base_margins = [20, 10, 20, 10]
                new_margins = [int(m * padding_scale) for m in base_margins]
                layout.setContentsMargins(*new_margins)
        
        self.logger.debug(f"控制面板响应式适配完成: {breakpoint}, 按钮大小: {button_size}")
    
    def _apply_table_responsive(self, breakpoint: str, config: Dict[str, Any]):
        """
        应用表格的响应式配置
        
        Args:
            breakpoint: 当前断点
            config: 响应式配置
        """
        table_density = config.get('table_density', 'normal')
        
        # 如果有表格实例，应用密度设置
        if hasattr(self, 'table_widget') and hasattr(self.table_widget, 'set_density'):
            self.table_widget.set_density(table_density)
            self.logger.debug(f"表格密度调整: {table_density}")
    
    def get_current_responsive_state(self) -> Dict[str, Any]:
        """
        获取当前组件的响应式状态
        
        Returns:
            当前响应式状态信息
        """
        return {
            'component': 'MainWorkspaceArea',
            'control_panel_height': self.control_panel.height() if hasattr(self, 'control_panel') else None,
            'search_width': self.search_input.minimumWidth() if hasattr(self, 'search_input') else None,
            'buttons_visible': len([btn for btn in self.control_panel_widgets.get('buttons', []) if btn.isVisible()]) if hasattr(self, 'control_panel_widgets') else None
        }


class MaterialFooterWidget(QWidget):
    """
    Material Design风格Footer组件
    
    显示系统状态、版权信息等。
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(50)  # 默认高度
        self.setup_ui()
    
    def setup_ui(self):
        """
        设置Footer界面
        """
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # 版权信息
        copyright_label = QLabel("© 2025 月度工资异动处理系统 - 高保真原型版本")
        copyright_label.setStyleSheet("color: #666; font-size: 12px;")
        
        # 状态信息
        status_label = QLabel("系统运行正常")
        status_label.setStyleSheet("color: #4CAF50; font-size: 12px; font-weight: bold;")
        
        layout.addWidget(copyright_label)
        layout.addStretch()
        layout.addWidget(status_label)
    
    def show_message(self, message: str, timeout: int = 0):
        """
        在状态栏显示消息。
        
        Args:
            message: 要显示的消息。
            timeout: 消息显示时间（毫秒），0表示永久显示直到下一次更新。
        """
        if hasattr(self, 'status_label'):
            self.status_label.setText(message)
            self.status_label.setStyleSheet("color: #2196F3; font-size: 12px; font-weight: bold;") # Info color
            
            # 如果设置了超时，则在超时后恢复默认消息
            if timeout > 0:
                QTimer.singleShot(timeout, self.clear_message)

    def clear_message(self):
        """清除消息，恢复默认状态。"""
        if hasattr(self, 'status_label'):
            self.status_label.setText("系统运行正常")
            self.status_label.setStyleSheet("color: #4CAF50; font-size: 12px; font-weight: bold;") # Success color

    def handle_responsive_change(self, breakpoint: str, config: Dict[str, Any]):
        """
        处理响应式变化
        
        Args:
            breakpoint: 断点名称
            config: 布局配置
        """
        footer_height = config.get('footer_height', 50)
        self.setFixedHeight(footer_height)


class MenuBarManager(QObject):
    """
    菜单栏管理器
    
    负责管理隐藏式菜单栏的显示/隐藏、创建菜单结构、保存状态等功能
    """
    
    # 信号定义
    visibility_changed = pyqtSignal(bool)  # 菜单栏可见性改变信号
    menu_action_triggered = pyqtSignal(str)  # 菜单动作触发信号
    
    def __init__(self, main_window: QMainWindow, config_manager: 'ConfigManager'):
        super().__init__()
        self.main_window = main_window
        self.config_manager = config_manager
        self.logger = setup_logger(__name__ + ".MenuBarManager")
        
        # 菜单栏实例
        self.menu_bar = None
        self.is_visible = False
        
        # 动画
        self.show_animation = None
        self.hide_animation = None
        
        # 初始化菜单栏
        self._create_menu_bar()
        self._load_visibility_state()
        
        self.logger.info("菜单栏管理器初始化完成")
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        try:
            self.menu_bar = self.main_window.menuBar()
            
            # 清空现有菜单以防止重复
            self.menu_bar.clear()
            
            # 创建菜单
            self._create_file_menu()
            self._create_data_menu()
            self._create_change_menu()
            self._create_report_menu()
            self._create_settings_menu()
            self._create_help_menu()
            
            # 设置菜单栏样式
            self._set_menu_bar_style()
            
            # 默认隐藏菜单栏
            self.menu_bar.setVisible(False)
            
            self.logger.info("菜单栏创建完成")
            
        except Exception as e:
            self.logger.error(f"创建菜单栏失败: {e}")
    
    def _create_file_menu(self):
        """创建文件菜单"""
        file_menu = self.menu_bar.addMenu('文件(&F)')
        
        # 导入数据
        import_action = QAction('导入数据(&I)', self.main_window)
        import_action.setShortcut('Ctrl+I')
        import_action.setStatusTip('导入Excel或CSV数据文件')
        import_action.triggered.connect(lambda: self._emit_menu_action('import_data'))
        file_menu.addAction(import_action)
        
        # 导出数据
        export_action = QAction('导出数据(&E)', self.main_window)
        export_action.setShortcut('Ctrl+E')
        export_action.setStatusTip('导出当前数据')
        export_action.triggered.connect(lambda: self._emit_menu_action('export_data'))
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 导入历史
        import_history_action = QAction('导入历史(&H)', self.main_window)
        import_history_action.setStatusTip('查看数据导入历史记录')
        import_history_action.triggered.connect(lambda: self._emit_menu_action('import_history'))
        file_menu.addAction(import_history_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出(&Q)', self.main_window)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.setStatusTip('退出系统')
        exit_action.triggered.connect(lambda: self._emit_menu_action('exit'))
        file_menu.addAction(exit_action)
    
    def _create_data_menu(self):
        """创建数据菜单"""
        data_menu = self.menu_bar.addMenu('数据(&D)')
        
        # 基准数据管理
        baseline_action = QAction('基准数据管理(&B)', self.main_window)
        baseline_action.setStatusTip('管理基准数据')
        baseline_action.triggered.connect(lambda: self._emit_menu_action('baseline_management'))
        data_menu.addAction(baseline_action)
        
        # 数据验证
        validate_action = QAction('数据验证(&V)', self.main_window)
        validate_action.setShortcut('Ctrl+Shift+V')
        validate_action.setStatusTip('验证数据的完整性和准确性')
        validate_action.triggered.connect(lambda: self._emit_menu_action('data_validation'))
        data_menu.addAction(validate_action)
        
        data_menu.addSeparator()
        
        # 数据备份
        backup_action = QAction('数据备份(&K)', self.main_window)
        backup_action.setStatusTip('备份当前数据')
        backup_action.triggered.connect(lambda: self._emit_menu_action('data_backup'))
        data_menu.addAction(backup_action)
        
        # 系统日志
        log_action = QAction('系统日志(&L)', self.main_window)
        log_action.setStatusTip('查看系统运行日志')
        log_action.triggered.connect(lambda: self._emit_menu_action('system_log'))
        data_menu.addAction(log_action)
    
    def _create_change_menu(self):
        """创建异动菜单"""
        change_menu = self.menu_bar.addMenu('异动(&C)')
        
        # 异动检测
        detect_action = QAction('异动检测(&D)', self.main_window)
        detect_action.setShortcut('F5')
        detect_action.setStatusTip('开始异动检测')
        detect_action.triggered.connect(lambda: self._emit_menu_action('change_detection'))
        change_menu.addAction(detect_action)
        
        change_menu.addSeparator()
        
        # 新增人员
        add_person_action = QAction('新增人员(&A)', self.main_window)
        add_person_action.setStatusTip('新增人员记录')
        add_person_action.triggered.connect(lambda: self._emit_menu_action('add_person'))
        change_menu.addAction(add_person_action)
        
        # 离职人员
        remove_person_action = QAction('离职人员(&R)', self.main_window)
        remove_person_action.setStatusTip('处理离职人员')
        remove_person_action.triggered.connect(lambda: self._emit_menu_action('remove_person'))
        change_menu.addAction(remove_person_action)
        
        # 工资调整
        salary_adjust_action = QAction('工资调整(&S)', self.main_window)
        salary_adjust_action.setStatusTip('处理工资调整')
        salary_adjust_action.triggered.connect(lambda: self._emit_menu_action('salary_adjustment'))
        change_menu.addAction(salary_adjust_action)
        
        change_menu.addSeparator()
        
        # 检测结果
        result_action = QAction('检测结果(&T)', self.main_window)
        result_action.setShortcut('Ctrl+R')
        result_action.setStatusTip('查看异动检测结果')
        result_action.triggered.connect(lambda: self._emit_menu_action('detection_results'))
        change_menu.addAction(result_action)
    
    def _create_report_menu(self):
        """创建报告菜单"""
        report_menu = self.menu_bar.addMenu('报告(&R)')
        
        # 生成Word报告
        word_report_action = QAction('生成Word报告(&W)', self.main_window)
        word_report_action.setShortcut('F6')
        word_report_action.setStatusTip('生成Word格式的异动报告')
        word_report_action.triggered.connect(lambda: self._emit_menu_action('generate_word_report'))
        report_menu.addAction(word_report_action)
        
        # 生成Excel分析表
        excel_report_action = QAction('生成Excel分析表(&E)', self.main_window)
        excel_report_action.setShortcut('F7')
        excel_report_action.setStatusTip('生成Excel格式的数据分析表')
        excel_report_action.triggered.connect(lambda: self._emit_menu_action('generate_excel_report'))
        report_menu.addAction(excel_report_action)
        
        report_menu.addSeparator()
        
        # 自定义报告
        custom_report_action = QAction('自定义报告(&C)', self.main_window)
        custom_report_action.setStatusTip('创建自定义报告')
        custom_report_action.triggered.connect(lambda: self._emit_menu_action('custom_report'))
        report_menu.addAction(custom_report_action)
        
        # 报告模板管理
        template_action = QAction('报告模板管理(&T)', self.main_window)
        template_action.setStatusTip('管理报告模板')
        template_action.triggered.connect(lambda: self._emit_menu_action('report_templates'))
        report_menu.addAction(template_action)
        
        report_menu.addSeparator()
        
        # 报告管理窗口
        manage_action = QAction('报告管理窗口(&M)', self.main_window)
        manage_action.setShortcut('F8')
        manage_action.setStatusTip('打开报告管理窗口')
        manage_action.triggered.connect(lambda: self._emit_menu_action('report_management'))
        report_menu.addAction(manage_action)
    
    def _create_settings_menu(self):
        """创建设置菜单"""
        settings_menu = self.menu_bar.addMenu('设置(&S)')
        
        # 用户设置
        user_settings_action = QAction('用户设置(&U)', self.main_window)
        user_settings_action.setStatusTip('用户个人设置')
        user_settings_action.triggered.connect(lambda: self._emit_menu_action('user_settings'))
        settings_menu.addAction(user_settings_action)
        
        # 系统设置
        system_settings_action = QAction('系统设置(&S)', self.main_window)
        system_settings_action.setStatusTip('系统配置和设置')
        system_settings_action.triggered.connect(lambda: self._emit_menu_action('system_settings'))
        settings_menu.addAction(system_settings_action)
        
        settings_menu.addSeparator()
        
        # 偏好设置
        preferences_action = QAction('偏好设置(&P)', self.main_window)
        preferences_action.setShortcut('Ctrl+,')
        preferences_action.setStatusTip('系统偏好设置')
        preferences_action.triggered.connect(lambda: self._emit_menu_action('preferences'))
        settings_menu.addAction(preferences_action)
    
    def _create_help_menu(self):
        """创建帮助菜单"""
        help_menu = self.menu_bar.addMenu('帮助(&H)')
        
        # 用户手册
        manual_action = QAction('用户手册(&M)', self.main_window)
        manual_action.setShortcut('F1')
        manual_action.setStatusTip('查看用户手册')
        manual_action.triggered.connect(lambda: self._emit_menu_action('user_manual'))
        help_menu.addAction(manual_action)
        
        help_menu.addSeparator()
        
        # 关于
        about_action = QAction('关于(&A)', self.main_window)
        about_action.setStatusTip('关于本系统')
        about_action.triggered.connect(lambda: self._emit_menu_action('about'))
        help_menu.addAction(about_action)
    
    def _set_menu_bar_style(self):
        """设置菜单栏样式"""
        if self.menu_bar:
            style = """
                QMenuBar {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    padding: 4px;
                    font-size: 14px;
                }
                
                QMenuBar::item {
                    background-color: transparent;
                    padding: 8px 12px;
                    border-radius: 4px;
                    margin: 0px 2px;
                }
                
                QMenuBar::item:selected {
                    background-color: #1976D2;
                }
                
                QMenuBar::item:pressed {
                    background-color: #1565C0;
                }
                
                QMenu {
                    background-color: white;
                    color: #212121;
                    border: 1px solid #E0E0E0;
                    border-radius: 4px;
                    padding: 4px;
                }
                
                QMenu::item {
                    padding: 8px 24px;
                    border-radius: 4px;
                    margin: 1px;
                }
                
                QMenu::item:selected {
                    background-color: #E3F2FD;
                    color: #1976D2;
                }
                
                QMenu::separator {
                    height: 1px;
                    background-color: #E0E0E0;
                    margin: 4px 8px;
                }
            """
            self.menu_bar.setStyleSheet(style)
    
    def _emit_menu_action(self, action_name: str):
        """发出菜单动作信号"""
        self.logger.debug(f"菜单动作触发: {action_name}")
        self.menu_action_triggered.emit(action_name)
    
    def toggle_visibility(self):
        """切换菜单栏显示状态"""
        if self.is_visible:
            self.hide_menu_bar()
        else:
            self.show_menu_bar()
    
    def show_menu_bar(self):
        """
        显示菜单栏
        """
        try:
            if self.menu_bar:
                self.menu_bar.setVisible(True)
                self.is_visible = True
                
                # 更新Header中设置按钮的状态
                if hasattr(self.main_window, 'header_widget'):
                    self.main_window.header_widget.update_settings_button_state(True)
                
                # 保存状态
                self._save_visibility_state()
                
                # 刷新主窗口布局 - 关键步骤
                self._refresh_main_window_layout()
                
                # 发射信号
                self.visibility_changed.emit(True)
                
                self.logger.info("菜单栏已显示")
                
        except Exception as e:
            self.logger.error(f"显示菜单栏失败: {e}")

    def hide_menu_bar(self):
        """
        隐藏菜单栏
        """
        try:
            if self.menu_bar:
                self.menu_bar.setVisible(False)
                self.is_visible = False
                
                # 更新Header中设置按钮的状态
                if hasattr(self.main_window, 'header_widget'):
                    self.main_window.header_widget.update_settings_button_state(False)
                
                # 保存状态
                self._save_visibility_state()
                
                # 刷新主窗口布局 - 关键步骤
                self._refresh_main_window_layout()
                
                # 发射信号
                self.visibility_changed.emit(False)
                
                self.logger.info("菜单栏已隐藏")
                
        except Exception as e:
            self.logger.error(f"隐藏菜单栏失败: {e}")

    def _refresh_main_window_layout(self):
        """
        刷新主窗口布局
        
        确保菜单栏显示/隐藏后，所有组件都能正确适应新的空间分配
        """
        try:
            if self.main_window:
                # 1. 暂停响应式管理器以避免冲突
                if hasattr(self.main_window, 'responsive_manager') and self.main_window.responsive_manager:
                    if hasattr(self.main_window.responsive_manager, 'pause_updates'):
                        self.main_window.responsive_manager.pause_updates()
                
                # 2. 清除表头缓存，防止按钮重影
                self._clear_table_header_cache()
                
                # 3. 强制更新主窗口布局
                self.main_window.update()
                
                # 4. 延迟执行详细布局刷新，确保组件稳定
                QTimer.singleShot(50, self._delayed_layout_refresh)
                
                # 5. 重新启动响应式管理器
                if hasattr(self.main_window, 'responsive_manager') and self.main_window.responsive_manager:
                    if hasattr(self.main_window.responsive_manager, 'resume_updates'):
                        QTimer.singleShot(150, self.main_window.responsive_manager.resume_updates)
                    elif hasattr(self.main_window.responsive_manager, 'force_update'):
                        QTimer.singleShot(150, self.main_window.responsive_manager.force_update)
                
                self.logger.debug("主窗口布局刷新完成")
                
        except Exception as e:
            self.logger.error(f"刷新主窗口布局失败: {e}")
    
    def _clear_table_header_cache(self):
        """
        清除表头缓存，解决按钮重影问题
        """
        try:
            # 查找所有表格组件并清除表头缓存
            tables = self.main_window.findChildren(QTableWidget)
            
            for table in tables:
                # 清除表头缓存
                h_header = table.horizontalHeader()
                v_header = table.verticalHeader()
                
                if h_header:
                    # 强制重绘表头
                    h_header.updateGeometry()
                    h_header.update()
                    h_header.repaint()
                    
                    # 清除选择状态避免重影
                    if hasattr(h_header, 'clearSelection'):
                        h_header.clearSelection()
                
                if v_header:
                    v_header.updateGeometry()
                    v_header.update()
                    v_header.repaint()
                
                # 清除表格视口缓存
                viewport = table.viewport()
                if viewport:
                    viewport.update()
                    viewport.repaint()
                
                self.logger.debug(f"已清除表格 {table.objectName()} 的表头缓存")
                
        except Exception as e:
            self.logger.error(f"清除表头缓存失败: {e}")
    
    def _delayed_layout_refresh(self):
        """
        延迟布局刷新，确保组件稳定后再更新
        """
        try:
            # 1. 更新中央部件
            central_widget = self.main_window.centralWidget()
            if central_widget:
                central_widget.updateGeometry()
                central_widget.update()
            
            # 2. 更新主要组件（避免重复更新）
            components_to_update = [
                ('main_workspace', getattr(self.main_window, 'main_workspace', None)),
                ('navigation_panel', getattr(self.main_window, 'navigation_panel', None)),
            ]
            
            for name, component in components_to_update:
                if component:
                    component.updateGeometry()
                    component.update()
                    
                    # 特殊处理表格组件
                    if hasattr(component, 'expandable_table'):
                        table = component.expandable_table
                        if table and isinstance(table, QTableWidget):
                            self._refresh_single_table(table)
                    
                    self.logger.debug(f"已更新组件: {name}")
            
            # 3. 最后清理一次表头
            QTimer.singleShot(50, self._final_header_cleanup)
            
        except Exception as e:
            self.logger.error(f"延迟布局刷新失败: {e}")
    
    def _refresh_single_table(self, table):
        """
        刷新单个表格组件
        
        Args:
            table: 表格组件
        """
        try:
            # 1. 更新表格本身
            table.updateGeometry()
            table.update()
            
            # 2. 重新设置表头模式以避免重影
            h_header = table.horizontalHeader()
            if h_header:
                # 保存当前设置
                current_resize_mode = QHeaderView.Stretch
                if table.columnCount() > 0:
                    try:
                        current_resize_mode = h_header.sectionResizeMode(0)
                    except:
                        current_resize_mode = QHeaderView.Stretch
                
                # 重新应用设置
                h_header.setSectionResizeMode(current_resize_mode)
                h_header.setStretchLastSection(True)
                
                # 强制更新
                h_header.updateGeometry()
                h_header.update()
            
            # 3. 更新视口
            viewport = table.viewport()
            if viewport:
                viewport.update()
            
            self.logger.debug("单个表格刷新完成")
            
        except Exception as e:
            self.logger.error(f"刷新单个表格失败: {e}")
    
    def _final_header_cleanup(self):
        """
        最终表头清理，确保没有重影
        """
        try:
            tables = self.main_window.findChildren(QTableWidget)
            
            for table in tables:
                # 最后一次强制重绘
                h_header = table.horizontalHeader()
                if h_header:
                    h_header.repaint()
                
                v_header = table.verticalHeader()
                if v_header:
                    v_header.repaint()
                
                # 确保视口清洁
                viewport = table.viewport()
                if viewport:
                    viewport.repaint()
            
            self.logger.debug("最终表头清理完成")
            
        except Exception as e:
            self.logger.error(f"最终表头清理失败: {e}")
    
    def _save_visibility_state(self):
        """保存菜单栏显示状态"""
        try:
            # 保存到用户偏好设置
            from src.modules.system_config.user_preferences import UserPreferences
            user_prefs = UserPreferences(self.config_manager)
            user_prefs.set_preference("ui.show_menubar", self.is_visible)
            
            self.logger.debug(f"菜单栏显示状态已保存: {self.is_visible}")
            
        except Exception as e:
            self.logger.error(f"保存菜单栏状态失败: {e}")
    
    def _load_visibility_state(self):
        """加载菜单栏显示状态"""
        try:
            # 从用户偏好设置加载
            from src.modules.system_config.user_preferences import UserPreferences
            user_prefs = UserPreferences(self.config_manager)
            self.is_visible = user_prefs.get_preference("ui.show_menubar", False)  # 默认隐藏
            
            if self.menu_bar:
                self.menu_bar.setVisible(self.is_visible)
            
            self.logger.debug(f"菜单栏显示状态已加载: {self.is_visible}")
            
        except Exception as e:
            self.logger.error(f"加载菜单栏状态失败: {e}")
            self.is_visible = False  # 默认隐藏

class PrototypeMainWindow(QMainWindow):
    """
    PyQt5重构高保真原型主窗口
    
    实现完整的三段式布局、响应式设计、Material Design风格。
    """
    
    # 窗口信号
    window_resized = pyqtSignal(QSize)

    # 核心服务信号
    import_requested = pyqtSignal(str) # 参数为建议的目标路径

    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, dynamic_table_manager: DynamicTableManager):
        super().__init__()
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.dynamic_table_manager = dynamic_table_manager
        
        # 初始化logger
        self.logger = setup_logger(f"{__name__}.PrototypeMainWindow")
        self.logger.info("PrototypeMainWindow: 正在初始化...")

        # --- 新架构核心组件初始化 ---
        self.app_state_service = get_application_state_service()
        self.app_controller = get_application_controller(self.dynamic_table_manager)
        
        # 初始化分页缓存管理器
        self.pagination_cache = PaginationCacheManager(
            max_cache_entries=50,
            max_memory_mb=50,
            ttl_seconds=300
        )
        self.logger.info(f"PrototypeMainWindow: 分页缓存管理器初始化完成")

        # 设置线程池
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(4)
        self.logger.info(f"PrototypeMainWindow: 线程池初始化完成，最大线程数: {self.thread_pool.maxThreadCount()}")

        # 设置管理器
        self._setup_managers()
        self.logger.info("PrototypeMainWindow: _setup_managers() 完成")

        # 设置UI
        self._setup_ui()
        self.logger.info("PrototypeMainWindow: _setup_ui() 完成")

        # 设置信号连接
        self._setup_connections()
        self.logger.info("PrototypeMainWindow: _setup_connections() 完成")

        # 加载初始数据
        self._load_field_mappings_from_file()
        self.logger.info("PrototypeMainWindow: _load_field_mappings_from_file() 完成")
        
        # 暂时跳过数据初始化，确保窗口能稳定显示
        # TODO: 稍后恢复数据初始化
        self.logger.info("PrototypeMainWindow: 跳过数据初始化，确保窗口稳定显示")

        self.logger.info("PrototypeMainWindow: __init__ 执行完毕")

    def _delayed_initialization(self):
        """延迟初始化，在窗口显示后执行"""
        try:
            self.logger.info("开始执行延迟初始化...")

            # 初始加载数据
            self.main_workspace.set_data(None)
            self.logger.info("main_workspace.set_data() with None 完成")

            # 暂时跳过启动时的表头清理，避免初始化阻塞
            # self._conditional_header_cleanup()
            self.logger.info("跳过启动时的表头清理，提升启动速度")

            self.logger.info("延迟初始化完成")

        except Exception as e:
            self.logger.error(f"延迟初始化失败: {e}", exc_info=True)

    def _setup_ui(self):
        """设置主窗口的用户界面"""
        # 设置窗口基本属性
        self.setWindowTitle("月度工资异动处理系统 - 高保真原型 v2.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 设置窗口图标
        self._set_window_icon()

        # 应用用户偏好设置（包括窗口几何）
        self._apply_user_preferences()

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局（垂直三段式）
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Header区域
        self.header_widget = MaterialHeaderWidget()
        main_layout.addWidget(self.header_widget)
        
        # Workspace区域（水平分割）
        self.main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧导航面板
        self.navigation_panel = EnhancedNavigationPanel(
            dynamic_table_manager=self.dynamic_table_manager,
            comm_manager=self.comm_manager
        )
        self.main_splitter.addWidget(self.navigation_panel)
        
        # 主工作区域
        self.main_workspace = MainWorkspaceArea(
            table_manager=self.dynamic_table_manager
        )
        self.main_splitter.addWidget(self.main_workspace)
        
        # 将分割器设置为主布局
        main_layout.addWidget(self.main_splitter)
        
        self.footer_widget = MaterialFooterWidget()
        main_layout.addWidget(self.footer_widget)
        
        self.setCentralWidget(central_widget)

        # 设置快捷键
        self._setup_shortcuts()

        self.logger.info("主窗口UI设置完成。")
    
    def _setup_shortcuts(self):
        """设置快捷键"""
        try:
            # Ctrl+M 切换菜单栏显示/隐藏
            toggle_menu_action = QAction(self)
            toggle_menu_action.setShortcut('Ctrl+M')
            toggle_menu_action.triggered.connect(self._on_settings_clicked)
            self.addAction(toggle_menu_action)
            
            self.logger.info("快捷键设置完成")
            
        except Exception as e:
            self.logger.error(f"设置快捷键失败: {e}")

    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            import os
            from PyQt5.QtGui import QIcon
            # 尝试设置自定义图标
            icon_path = os.path.join("resources", "icons", "app_icon.png")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
            else:
                # 使用默认图标
                self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
        except Exception as e:
            self.logger.warning(f"设置窗口图标失败: {e}")

    def _apply_user_preferences(self):
        """应用用户偏好设置"""
        try:
            # 初始化用户偏好设置（用于保存UI状态等）
            self.user_preferences = UserPreferences()

            preferences = self.user_preferences.get_all_preferences()

            if 'window_geometry' in preferences and preferences['window_geometry']:
                # 从base64字符串恢复QByteArray
                import base64
                from PyQt5.QtCore import QByteArray
                geometry_data = base64.b64decode(preferences['window_geometry'])
                geometry_array = QByteArray(geometry_data)
                self.restoreGeometry(geometry_array)
                self.logger.info("已恢复窗口几何设置")

        except Exception as e:
            self.logger.warning(f"应用用户偏好设置失败: {e}")

    def _setup_managers(self):
        """初始化各种管理器"""
        # 响应式布局管理器
        self.responsive_manager = ResponsiveLayoutManager(self)
        
        # 组件通信管理器
        self.comm_manager = ComponentCommunicationManager(self)
        
        # 菜单栏管理器
        self.menu_bar_manager = MenuBarManager(self, self.config_manager)
        
        # 表头管理器（增强版）
        self.header_manager = get_global_header_manager()
        self.logger.info("管理器设置完成，包含增强版表头管理器")

    def _setup_connections(self):
        """设置信号连接"""
        try:
            # 1. Header组件信号连接
            self.header_widget.settings_clicked.connect(self._on_settings_clicked)
            
            # 2. 主工作区信号连接
            self.main_workspace.import_requested.connect(self._on_import_data_requested)
            
            # 3. 响应式布局信号连接
            self.responsive_manager.breakpoint_changed.connect(self._on_responsive_layout_changed)
            
            # 4. 菜单栏信号连接
            self.menu_bar_manager.visibility_changed.connect(self._on_menu_visibility_changed)
            self.menu_bar_manager.menu_action_triggered.connect(self._on_menu_action_triggered)
            
            # 5. 导航面板信号连接
            self.navigation_panel.navigation_changed.connect(self._on_navigation_changed)
            
            # 6. 表格组件信号连接
            if hasattr(self.main_workspace, 'data_table') and self.main_workspace.data_table:
                # 连接字段映射更新信号
                self.main_workspace.data_table.field_mapping_updated.connect(self._on_field_mapping_updated)
                # 连接表头编辑信号
                self.main_workspace.data_table.header_edited.connect(self._on_header_edited)
            
            self.logger.info("信号连接设置完成")
            
        except Exception as e:
            self.logger.error(f"设置信号连接失败: {e}", exc_info=True)
    
    @pyqtSlot()
    def _on_settings_clicked(self):
        """处理设置按钮点击事件"""
        try:
            if self.menu_bar_manager:
                # 切换菜单栏显示状态
                self.menu_bar_manager.toggle_visibility()
                self.logger.info("设置按钮点击，切换菜单栏显示状态")
            else:
                self.logger.warning("菜单栏管理器未初始化")
                
        except Exception as e:
            self.logger.error(f"处理设置按钮点击失败: {e}")
    
    @pyqtSlot(bool)
    def _on_menu_visibility_changed(self, visible: bool):
        """
        菜单栏可见性变化处理（增强版，仅在必要时清理表头）
        
        Args:
            visible: 菜单栏是否可见
        """
        try:
            self.logger.info(f"菜单栏可见性变化: {visible}")
            
            # 触发全面的布局刷新
            self.refresh_layout()
            
            # 仅在菜单栏切换时，延迟执行一次表头清理
            QTimer.singleShot(500, lambda: self._conditional_header_cleanup())
            
            # 更新状态信息
            status_msg = "菜单栏已显示" if visible else "菜单栏已隐藏"
            if hasattr(self, 'main_workspace'):
                self.main_workspace._update_status_label(status_msg, "info")
                
        except Exception as e:
            self.logger.error(f"处理菜单栏可见性变化失败: {e}")

    def refresh_layout(self):
        """
        全面刷新窗口布局（增强版，集成表头管理器）
        
        用于菜单栏状态变化等需要重新调整整体布局的场景
        """
        try:
            # 1. 预先注册所有表格到表头管理器
            self._register_all_tables_to_header_manager()
            
            # 2. 更新窗口自身
            self.update()
            self.repaint()
            
            # 3. 更新中央部件
            central_widget = self.centralWidget()
            if central_widget:
                central_widget.updateGeometry()
                central_widget.update()
            
            # 4. 更新主要组件
            components = [
                ('header_widget', self.header_widget if hasattr(self, 'header_widget') else None),
                ('navigation_panel', self.navigation_panel if hasattr(self, 'navigation_panel') else None),
                ('main_workspace', self.main_workspace if hasattr(self, 'main_workspace') else None),
                ('footer_widget', self.footer_widget if hasattr(self, 'footer_widget') else None)
            ]
            
            for name, component in components:
                if component:
                    component.updateGeometry()
                    component.update()
                    self.logger.debug(f"已刷新组件: {name}")
            
            # 4. 触发响应式管理器更新 (延迟执行，确保布局稳定)
            if hasattr(self, 'responsive_manager') and self.responsive_manager:
                QTimer.singleShot(100, self.responsive_manager.force_update)
            
            # 5. 特别处理表格组件的视口更新
            if hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'data_table') and self.main_workspace.data_table:
                    self.main_workspace.data_table.viewport().update()
                    self.main_workspace.data_table.updateGeometry()
            
            self.logger.info("窗口布局全面刷新完成")
            
        except Exception as e:
            self.logger.error(f"刷新窗口布局失败: {e}")

    @pyqtSlot(str)
    def _on_menu_action_triggered(self, action_name: str):
        """处理菜单动作触发"""
        try:
            self.logger.info(f"菜单动作触发: {action_name}")
            
            # 根据动作名称执行相应操作
            if action_name == "import_data":
                self._on_import_data_requested()
            elif action_name == "export_report":
                self.main_workspace._on_export_report()
            elif action_name == "show_about":
                self._show_about_dialog()
            # 可以根据需要添加更多菜单动作处理
            
        except Exception as e:
            self.logger.error(f"处理菜单动作失败: {action_name}, 错误: {e}")

    @pyqtSlot(str, dict)
    def _on_field_mapping_updated(self, table_name: str, mapping: dict):
        """处理字段映射更新 - 使用统一的ConfigSyncManager"""
        try:
            self.logger.info(f"字段映射已更新: {table_name}")
            
            # 更新内存中的映射
            self.field_mappings[table_name] = mapping
            
            # 使用ConfigSyncManager保存映射
            if hasattr(self, 'config_sync_manager'):
                success = self.config_sync_manager.save_mapping(table_name, mapping)
                if success:
                    self.logger.info(f"字段映射已成功保存到ConfigSyncManager: {table_name}")
                else:
                    self.logger.error(f"保存字段映射到ConfigSyncManager失败: {table_name}")
            
            # 重新加载当前表格数据以应用新的映射
            if hasattr(self, 'current_nav_path') and len(self.current_nav_path) == 4:
                current_table_name = self._generate_table_name_from_path(self.current_nav_path)
                if current_table_name == table_name:
                    self.logger.info(f"重新加载当前表格以应用新的字段映射: {table_name}")
                    self._reload_current_table_data()
            
        except Exception as e:
            self.logger.error(f"处理字段映射更新失败: {e}", exc_info=True)

    @pyqtSlot(int, str, str)
    def _on_header_edited(self, column: int, old_name: str, new_name: str):
        """处理表头编辑"""
        try:
            self.logger.info(f"表头已编辑: 列{column}, {old_name} -> {new_name}")
            
            # 立即刷新表格显示以应用新的表头名称
            self._refresh_table_headers()
            
        except Exception as e:
            self.logger.error(f"处理表头编辑失败: {e}", exc_info=True)

    def _reload_current_table_data(self):
        """重新加载当前表格数据"""
        try:
            if hasattr(self, 'current_nav_path') and len(self.current_nav_path) == 4:
                table_name = self._generate_table_name_from_path(self.current_nav_path)
                
                # 检查是否是分页模式
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget and self.main_workspace.pagination_widget.isVisible():
                    # 分页模式：重新加载当前页
                    current_page = self.main_workspace.pagination_widget.get_current_page()
                    page_size = self.main_workspace.pagination_widget.get_page_size()
                    self._load_data_with_pagination(table_name, current_page, page_size)
                else:
                    # 普通模式：重新加载全部数据
                    worker = Worker(self._load_database_data_with_mapping, table_name)
                    worker.signals.result.connect(lambda df: self.main_workspace.set_data(df, preserve_headers=True, table_name=table_name))
                    self.thread_pool.start(worker)
                    
        except Exception as e:
            self.logger.error(f"重新加载当前表格数据失败: {e}", exc_info=True)

    def _refresh_table_headers(self):
        """刷新表格表头显示"""
        try:
            if hasattr(self.main_workspace, 'data_table') and self.main_workspace.data_table:
                # 直接调用表格的表头刷新方法
                self.main_workspace.data_table._refresh_header_display()
                self.logger.debug("表格表头显示已刷新")
                
        except Exception as e:
            self.logger.error(f"刷新表格表头显示失败: {e}", exc_info=True)

    def _show_about_dialog(self):
        """显示关于对话框"""
        try:
            about_text = """
            月度工资异动处理系统 v2.0
            
            这是一个现代化的工资异动处理系统，采用PyQt5技术栈开发。
            
            主要功能：
            • 数据导入和验证
            • 异动检测和分析  
            • 报告生成和导出
            • 隐藏式菜单栏
            
            开发团队：系统开发团队
            技术栈：Python + PyQt5 + SQLite
            """
            
            QMessageBox.about(self, "关于系统", about_text)
            
        except Exception as e:
            self.logger.error(f"显示关于对话框失败: {e}")
    
    @pyqtSlot(str, dict)
    def _on_responsive_layout_changed(self, breakpoint: str, config: dict):
        """处理响应式布局变化"""
        try:
            # 传递给各个组件
            if hasattr(self, 'header_widget'):
                self.header_widget.handle_responsive_change(breakpoint, config)
            
            if hasattr(self, 'main_workspace'):
                self.main_workspace.handle_responsive_change(breakpoint, config)
                
            if hasattr(self, 'footer_widget'):
                self.footer_widget.handle_responsive_change(breakpoint, config)
                
            self.logger.debug(f"响应式布局变化: {breakpoint}")
            
        except Exception as e:
            self.logger.error(f"处理响应式布局变化失败: {e}")
    
    @pyqtSlot()
    def _on_import_data_requested(self):
        """响应数据导入请求，打开导入对话框。"""
        suggested_path = self._get_suggested_target_path()
        self.logger.info(f"接收到数据导入请求，推断的目标路径: {suggested_path}。打开导入对话框。")

        dialog = DataImportDialog(
            parent=self, 
            dynamic_table_manager=self.dynamic_table_manager, 
            target_path=suggested_path
        )
        dialog.data_imported.connect(self._handle_data_imported)
        dialog.exec_()
    
    @pyqtSlot(dict)
    def _handle_data_imported(self, import_result: dict):
        """处理数据导入完成后的逻辑"""
        self.logger.info(f"开始处理导入结果: {import_result}")

        if not import_result.get("success"):
            error_message = import_result.get("error", "未知导入错误")
            self._show_error_message("导入失败", error_message)
            return

        import_mode = import_result.get("import_mode", "single_sheet")
        target_path_str = import_result.get("target_path", "")
        source_file = import_result.get("source_file", "未知来源")
        
        self.logger.info(f"导入模式: {import_mode}, 目标路径: '{target_path_str}'")

        if import_mode == 'single_sheet':
            df = import_result.get("dataframe")
            if df is None or df.empty:
                self._show_warning_message("导入结果", "导入成功，但未返回有效数据。")
                return
        
        self.logger.info(f"接收到导入数据, 来源: {source_file}, 目标路径: {target_path_str}")
        
        # 从目标路径字符串解析路径列表
        if target_path_str:
            target_path_list = target_path_str.split(' > ')
        else:
            # 如果没有目标路径，使用当前导航路径作为后备
            target_path_list = self.current_nav_path if self.current_nav_path else []
            self.logger.info(f"目标路径为空，使用当前导航路径作为后备: {target_path_list}")
            
        if not target_path_list or len(target_path_list) < 4:
            self.logger.error(f"目标路径不完整或无效: {target_path_list}。无法保存数据。")
            self._show_error_message("保存失败", f"目标路径不完整: {target_path_str}")
            # 即便路径不完整，也尝试刷新导航，以便用户能看到新导入的数据
            self._update_navigation_if_needed(target_path_list)
            return

        # 仅单Sheet模式需要处理字段映射和数据保存
        if import_mode == 'single_sheet':
            df = import_result.get("dataframe")
            field_mapping = import_result.get("field_mapping", {})

            # 保存字段映射信息
            if field_mapping:
                table_name = self._generate_table_name_from_path(target_path_list)
                self.logger.info(f"保存字段映射信息到表 {table_name}: {field_mapping}")
                self.field_mappings[table_name] = field_mapping
                
                # 使用ConfigSyncManager保存映射
                if hasattr(self, 'config_sync_manager'):
                    success = self.config_sync_manager.save_mapping(table_name, field_mapping)
                    if success:
                        self.logger.info(f"字段映射已成功保存到ConfigSyncManager: {table_name}")
                    else:
                        self.logger.error(f"保存字段映射到ConfigSyncManager失败: {table_name}")
                        # 如果ConfigSyncManager保存失败，回退到旧方法
                        self._save_field_mappings_to_file()

            # 保存到数据库
            self._save_to_database(df, target_path_list)
            
            # 如果导入的路径与当前选择的路径一致，刷新显示
            current_path_str = " > ".join(self.current_nav_path) if self.current_nav_path else ""
            if target_path_str == current_path_str:
                # 生成表名用于字段映射
                current_table_name = self._generate_table_name_from_path(self.current_nav_path) if self.current_nav_path else ""
                self.main_workspace.set_data(df, preserve_headers=True, table_name=current_table_name)
                self.logger.info("导入路径与当前路径一致，已刷新显示")
            else:
                self._show_info_message("导入成功", 
                    f"数据已成功导入到: {target_path_str}\n如需查看，请在左侧导航栏中选择对应位置。")
        
        # 对于所有成功的导入，都通知导航面板更新
        self._update_navigation_if_needed(target_path_list)
    
    def _update_navigation_if_needed(self, target_path_list: List[str]):
        """如果需要，更新导航面板（添加新的年份、月份或类别）"""
        try:
            self.logger.info(f"检查是否需要更新导航面板: {target_path_list}")
            
            # 检查是否是工资数据导入
            if len(target_path_list) >= 4 and target_path_list[0] == "工资表":
                self.logger.info("检测到工资数据导入，开始刷新导航面板")
                
                # 使用强制刷新工资数据导航的方法
                if hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'force_refresh_salary_data'):
                    self.logger.info("使用强制刷新方法")
                    self.navigation_panel.force_refresh_salary_data()
                    
                    # 延迟导航到新导入的路径，给刷新过程足够时间
                    target_path_str = " > ".join(target_path_list)
                    self.logger.info(f"将在800ms后导航到: {target_path_str}")
                    QTimer.singleShot(800, lambda: self._navigate_to_imported_path(target_path_str))
                    
                elif hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'refresh_navigation_data'):
                    self.logger.info("使用完整刷新方法")
                    # 后备方案：完整刷新导航面板
                    self.navigation_panel.refresh_navigation_data()
                    
                    # 延迟导航到新导入的路径，给完整刷新过程更多时间
                    target_path_str = " > ".join(target_path_list)
                    self.logger.info(f"将在1000ms后导航到: {target_path_str}")
                    QTimer.singleShot(1000, lambda: self._navigate_to_imported_path(target_path_str))
                    
                else:
                    self.logger.warning("导航面板不支持动态刷新")
            else:
                self.logger.info("非工资数据导入，使用标准刷新")
                # 非工资数据导入，使用标准刷新
                if hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'refresh_navigation_data'):
                    self.navigation_panel.refresh_navigation_data()
                    self.logger.info("导航面板已刷新")
                else:
                    self.logger.warning("导航面板不支持动态刷新")
            
        except Exception as e:
            self.logger.error(f"更新导航面板失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 不影响主要功能，只记录错误
    
    def _navigate_to_imported_path(self, target_path_str: str):
        """导航到新导入的数据路径"""
        try:
            self.logger.info(f"尝试导航到新导入的路径: {target_path_str}")
            
            if hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'navigate_to_path'):
                # 调用导航面板的导航方法
                self.navigation_panel.navigate_to_path(target_path_str)
                self.logger.info(f"已成功导航到新导入的路径: {target_path_str}")
                
                # 同时更新主窗口的状态栏提示
                self._update_status_label(f"已导航到新导入的数据: {target_path_str}", "success")
                
            else:
                self.logger.warning("导航面板不支持路径导航")
                # 至少更新状态栏告知用户
                self._update_status_label(f"数据导入成功，请在左侧导航栏查看: {target_path_str}", "info")
                
        except Exception as e:
            self.logger.error(f"导航到导入路径失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 即使导航失败，也要告知用户数据导入成功
            self._update_status_label(f"数据导入成功，请手动选择查看: {target_path_str}", "warning")

    def _save_field_mappings_to_file(self):
        """将字段映射信息保存到文件"""
        try:
            import json
            import os
            
            # 确保目录存在
            mapping_dir = "state/data"
            os.makedirs(mapping_dir, exist_ok=True)
            
            mapping_file = os.path.join(mapping_dir, "field_mappings.json")
            
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(self.field_mappings, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"字段映射信息已保存到: {mapping_file}")
            
        except Exception as e:
            self.logger.error(f"保存字段映射信息失败: {e}")

    def _load_field_mappings_from_file(self):
        """从文件加载字段映射信息 - 统一使用ConfigSyncManager"""
        try:
            from src.modules.data_import.config_sync_manager import ConfigSyncManager
            
            # 初始化ConfigSyncManager
            if not hasattr(self, 'config_sync_manager'):
                self.config_sync_manager = ConfigSyncManager()
            
            # 获取所有表名
            table_names = self.config_sync_manager.get_all_table_names()
            
            # 使用统一接口加载每个表的映射
            self.field_mappings = {}
            for table_name in table_names:
                mapping = self.config_sync_manager.load_mapping(table_name)
                if mapping:
                    self.field_mappings[table_name] = mapping
            
            self.logger.info(f"已加载字段映射信息，共{len(self.field_mappings)}个表的映射")
            
        except Exception as e:
            self.logger.error(f"加载字段映射信息失败: {e}")
            self.field_mappings = {}

    def _apply_field_mapping_to_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """对数据框应用字段映射，将数据库列名转换为中文显示名"""
        try:
            # 使用统一的ConfigSyncManager获取字段映射
            if hasattr(self, 'config_sync_manager'):
                field_mapping = self.config_sync_manager.load_mapping(table_name)
            else:
                field_mapping = self.field_mappings.get(table_name, {})
            
            if not field_mapping:
                self.logger.info(f"表 {table_name} 没有保存的字段映射信息")
                return df
            
            # 当前数据框的列名
            current_columns = list(df.columns)
            
            # 创建列名映射：当前列名 -> 映射后的显示名
            column_rename_map = {}
            
            for db_col in current_columns:
                # 直接从字段映射中查找显示名称
                if db_col in field_mapping:
                    display_name = field_mapping[db_col]
                    if display_name and display_name != db_col:  # 确保显示名不为空且不同于原名
                        column_rename_map[db_col] = display_name
            
            # 应用列名映射
            if column_rename_map:
                df_renamed = df.rename(columns=column_rename_map)
                self.logger.info(f"应用字段映射到表 {table_name}: {len(column_rename_map)} 个字段重命名")
                self.logger.debug(f"列名映射: {column_rename_map}")
                return df_renamed
            else:
                self.logger.info(f"表 {table_name} 无需字段重命名")
                return df
                
        except Exception as e:
            self.logger.error(f"应用字段映射失败: {e}", exc_info=True)
            return df

    def _on_navigation_changed(self, path: str, context: dict):
        """处理导航变化 - 支持分页模式自动检测"""
        self.logger.info(f"导航变化: {path}")
        self.current_nav_path = path.split(' > ')
        self.current_selection = context
        
        # 仅当路径指向最末级时才加载数据
        if len(self.current_nav_path) == 4:
            table_name = self._generate_table_name_from_path(self.current_nav_path)
            
            # 重置分页状态
            if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                self.main_workspace.pagination_widget.reset()
            
            # 先检查数据量，决定是否使用分页模式
            self._check_and_load_data_with_pagination(table_name)
        else:
            # 如果选择的不是最末级，则清空表格并提示
            self.main_workspace.set_data(None)
            self._show_empty_table_with_prompt("请在左侧选择一个具体的数据表进行查看。")

    def _check_and_load_data_with_pagination(self, table_name: str):
        """检查数据量并决定是否使用分页模式加载数据"""
        try:
            # 检查表是否存在
            if not self.dynamic_table_manager.table_exists(table_name):
                self.logger.info(f"数据表 {table_name} 尚未创建，请导入数据后重试")
                self.main_workspace.set_data(None)
                self._show_empty_table_with_prompt(f"数据表 {table_name} 尚未创建，请导入数据后重试")
                return
            
            # 获取总记录数
            total_records = self.dynamic_table_manager.get_table_record_count(table_name)
            
            if total_records == 0:
                self.logger.info(f"数据表 {table_name} 为空")
                self.main_workspace.set_data(None)
                self._show_empty_table_with_prompt(f"数据表 {table_name} 为空，请导入数据后重试")
                return
            
            # 判断是否需要启用分页模式
            pagination_threshold = 30  # 超过30条记录启用分页
            
            if total_records > pagination_threshold:
                self.logger.info(f"数据量大({total_records}条)，启用分页模式")
                self._load_data_with_pagination(table_name, page=1, page_size=50)
            else:
                self.logger.info(f"数据量小({total_records}条)，使用普通模式")
                # 隐藏分页组件
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                    self.main_workspace.pagination_widget.setVisible(False)
                
                # 普通加载全部数据
                worker = Worker(self._load_database_data_with_mapping, table_name)
                worker.signals.result.connect(lambda df: self.main_workspace.set_data(df, preserve_headers=True, table_name=table_name))
                self.thread_pool.start(worker)
                
        except Exception as e:
            self.logger.error(f"检查数据量失败: {e}", exc_info=True)
            self.main_workspace.set_data(None)
            self._show_empty_table_with_prompt(f"检查数据量失败: {e}")

    def _load_data_with_pagination(self, table_name: str, page: int = 1, page_size: int = 50):
        """使用分页模式加载数据（支持缓存）"""
        self.logger.info(f"使用分页模式加载 {table_name}，第{page}页，每页{page_size}条")
        
        # 显示分页组件
        if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
            self.main_workspace.pagination_widget.setVisible(True)
        
        # 尝试从缓存获取数据
        cached_data = self.pagination_cache.get_page_data(table_name, page, page_size)
        if cached_data is not None:
            self.logger.info(f"缓存命中: {table_name} 第{page}页")
            
            # 从缓存获取表信息
            table_info = self.pagination_cache.get_table_info(table_name)
            total_records = table_info.total_records if table_info else len(cached_data) * page
            
            # 应用字段映射
            df_with_mapping = self._apply_field_mapping_to_dataframe(cached_data, table_name)
            
            # 构造结果
            result = {
                'success': True,
                'data': df_with_mapping,
                'total_records': total_records,
                'current_page': page,
                'page_size': page_size,
                'table_name': table_name,
                'from_cache': True
            }
            
            # 直接处理结果
            self._on_pagination_data_loaded(result)
            
            # 异步预加载相邻页面
            if table_info:
                total_pages = (total_records + page_size - 1) // page_size
                self.pagination_cache.preload_adjacent_pages(
                    table_name, page, page_size, total_pages, 
                    self.dynamic_table_manager.get_dataframe_paginated
                )
            
            return
        
        # 缓存未命中，从数据库加载
        self.logger.info(f"缓存未命中，从数据库加载: {table_name} 第{page}页")
        
        # 创建分页工作线程
        worker = PaginationWorker(
            table_manager=self.dynamic_table_manager,
            table_name=table_name,
            page=page,
            page_size=page_size,
            apply_mapping_func=self._apply_field_mapping_to_dataframe
        )
        
        # 连接信号
        worker.signals.result.connect(self._on_pagination_data_loaded)
        
        # 启动工作线程
        self.thread_pool.start(worker)

    def _on_pagination_data_loaded(self, result: dict):
        """处理分页数据加载完成（支持缓存）"""
        try:
            if result.get('success', False):
                # 加载成功
                df = result['data']
                total_records = result['total_records']
                current_page = result['current_page']
                page_size = result['page_size']
                table_name = result['table_name']
                from_cache = result.get('from_cache', False)
                
                cache_msg = "（缓存）" if from_cache else "（数据库）"
                self.logger.info(f"分页数据加载成功{cache_msg}: {len(df)}条数据，第{current_page}页，总计{total_records}条")
                
                # 如果数据来自数据库，需要缓存它
                if not from_cache:
                    # 缓存页面数据
                    self.pagination_cache.put_page_data(table_name, current_page, page_size, df)
                    
                    # 缓存或更新表信息
                    columns = df.columns.tolist()
                    self.pagination_cache.put_table_info(table_name, total_records, columns)
                    
                    # 异步预加载相邻页面
                    total_pages = (total_records + page_size - 1) // page_size
                    self.pagination_cache.preload_adjacent_pages(
                        table_name, current_page, page_size, total_pages,
                        self.dynamic_table_manager.get_dataframe_paginated
                    )
                
                # 设置数据到表格（使用分页模式）
                self.main_workspace.set_data(df, preserve_headers=True, table_name=table_name)
                
                # 更新分页组件状态
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                    self.main_workspace.pagination_widget.set_total_records(total_records)
                    self.main_workspace.pagination_widget.set_current_page(current_page)
                    self.main_workspace.pagination_widget.set_page_size(page_size)
                
                # 更新状态信息（包含缓存状态）
                cache_stats = self.pagination_cache.get_cache_stats()
                status_msg = f"已加载 {table_name}，第{current_page}页，共{total_records}条记录{cache_msg}"
                if cache_stats:
                    status_msg += f" [缓存命中率: {cache_stats.get('hit_rate', 0)}%]"
                
                self._update_status_label(status_msg, "success")
                
            else:
                # 加载失败
                error = result.get('error', '未知错误')
                table_name = result.get('table_name', '未知表')
                self.logger.error(f"分页数据加载失败: {error}")
                
                self.main_workspace.set_data(None)
                self._show_empty_table_with_prompt(f"加载 {table_name} 失败: {error}")
                
        except Exception as e:
            self.logger.error(f"处理分页数据结果失败: {e}", exc_info=True)
            self.main_workspace.set_data(None)
            self._show_empty_table_with_prompt(f"处理分页数据失败: {e}")

    def _load_database_data_with_mapping(self, table_name: str) -> Optional[pd.DataFrame]:
        """从数据库加载数据并应用字段映射"""
        try:
            # 检查表是否存在
            if not self.dynamic_table_manager.table_exists(table_name):
                self.logger.info(f"数据表 {table_name} 尚未创建，请导入数据后重试")
                return None

            df = self.dynamic_table_manager.get_dataframe_from_table(table_name)
            
            if df is None or df.empty:
                self.logger.info(f"数据表 {table_name} 为空，请导入数据后重试")
                return None
            
            self.logger.info(f"成功从数据库加载 {len(df)} 行数据")
            
            # 应用字段映射，确保显示中文表头
            df_with_mapping = self._apply_field_mapping_to_dataframe(df, table_name)
            
            self.logger.info(f"应用字段映射后的表头: {list(df_with_mapping.columns)}")
            
            return df_with_mapping
            
        except Exception as e:
            self.logger.error(f"加载数据时出错: {e}", exc_info=True)
            return None

    def _load_database_data(self, table_name: str) -> Optional[pd.DataFrame]:
        """从数据库加载指定表的数据。"""
        try:
            # 检查表是否存在
            if not self.dynamic_table_manager.table_exists(table_name):
                self.logger.info(f"数据表 {table_name} 尚未创建，请导入数据后重试")
                return None

            df = self.dynamic_table_manager.get_dataframe_from_table(table_name)
            
            if df is None or df.empty:
                self.logger.info(f"数据表 {table_name} 为空，请导入数据后重试")
                return None
                
            self.logger.info(f"成功从数据库加载 {len(df)} 行数据。")
            return df
        except Exception as e:
            self.logger.error(f"加载数据时出错: {e}", exc_info=True)
            # 在主线程中显示错误消息
            # self.main_workspace.set_data(None) # 避免循环调用
            return None

    def _save_to_database(self, df: pd.DataFrame, category_node_path: list[str]):
        """将DataFrame保存到由导航路径决定的数据库表中。"""
        try:
            table_name = self._generate_table_name_from_path(category_node_path)
            self.logger.info(f"准备将 {len(df)} 条数据保存到表: {table_name}")
            worker = Worker(self._execute_save, df, table_name)
            worker.signals.result.connect(self._on_save_finished)
            self.thread_pool.start(worker)
        except Exception as e:
            self.logger.error(f"准备保存数据时出错: {e}", exc_info=True)
            self._show_error_message("数据库错误", f"保存数据失败: {e}")

    def _execute_save(self, df: pd.DataFrame, table_name: str) -> dict:
        """在工作线程中执行实际的数据库保存操作，并返回结果。"""
        try:
            success, message = self.dynamic_table_manager.save_dataframe_to_table(df, table_name)
            if success:
                return {"success": True, "table_name": table_name, "message": message}
            else:
                return {"success": False, "table_name": table_name, "error": message}
        except Exception as e:
            # This will catch cases where save_dataframe_to_table might not return a tuple
            # For example if it returns a single boolean False
            self.logger.error(f"异步保存数据到表 {table_name} 失败: {e}", exc_info=True)
            error_message = str(e) if str(e) else "发生未知异常"
            # Attempt to unpack if possible, otherwise treat as a single value
            try:
                success, msg = e
                error_message = msg
            except (TypeError, ValueError):
                pass
            return {"success": False, "table_name": table_name, "error": error_message}

    @pyqtSlot(dict)
    def _on_save_finished(self, result: dict):
        """处理数据库保存完成后的结果。"""
        success = result.get("success", False)
        table_name = result.get("table_name", "未知表")
        if success:
            self.logger.info(f"数据成功保存到表: {table_name}")
            self._show_info_message("保存成功", f"数据已成功保存到数据库表 '{table_name}'。")
        else:
            error = result.get("error", "未知错误")
            self.logger.error(f"保存数据到表 {table_name} 失败: {error}")
            self._show_error_message("保存失败", f"无法将数据保存到数据库表 '{table_name}'。\n错误: {error}")

    def _generate_table_name_from_path(self, path_list: list[str]) -> str:
        """根据导航路径列表生成数据库表名"""
        if len(path_list) < 4:
            self.logger.warning(f"导航路径不完整，无法生成表名: {path_list}")
            return ""

        base_name = "salary_data"
        year = re.sub(r'\D', '', path_list[1])
        month = re.sub(r'\D', '', path_list[2]).zfill(2)

        category = path_list[3]
        category_map = {
            "全部在职人员": "active_employees",
            "A岗职工": "a_grade_employees",
            "B岗职工": "b_grade_employees",
            "退休人员": "pension_employees",  # 故意映射到pension
            "离休人员": "retired_employees",  # 故意映射到retired
            "其他人员": "other_employees"
        }
        category_en = category_map.get(category, "unknown_category")
        
        return f"{base_name}_{year}_{month}_{category_en}"
        
    def _get_suggested_target_path(self) -> str:
        """根据当前状态智能推断建议的目标路径"""
        try:
            if hasattr(self, 'current_nav_path') and self.current_nav_path:
                return " > ".join(self.current_nav_path)
            else:
                # 如果没有当前路径，生成一个默认路径
                from datetime import datetime
                now = datetime.now()
                return f"工资表 > {now.year}年 > {now.month:02d}月 > 全部在职人员"
        except Exception as e:
            self.logger.warning(f"无法生成建议路径: {e}")
            return ""

    def _initialize_data(self):
        """初始化加载数据"""
        # 初始加载时显示空表格和提示信息
        if not self.main_workspace.set_data(None):
            self._show_empty_table_with_prompt("请从左侧导航栏选择要查看的数据表，或从菜单栏导入新数据。")

    def _show_empty_table_with_prompt(self, prompt: str):
        """显示带有提示的空表格。"""
        self.main_workspace.set_data(None)
        self._update_status_label(prompt, "info")

    @pyqtSlot(str, str)
    def _update_status_label(self, message: str, status_type: str = "info"):
        """更新状态标签"""
        try:
            # 简化版本，只记录日志
            self.logger.info(f"状态更新: {message} (类型: {status_type})")
        except Exception as e:
            self.logger.error(f"更新状态标签失败: {e}")
    
    def _show_info_message(self, title: str, message: str):
        """显示信息消息框。"""
        QMessageBox.information(self, title, message)

    def _show_warning_message(self, title: str, message: str):
        """显示警告消息框。"""
        QMessageBox.warning(self, title, message)

    def _show_error_message(self, title: str, message: str):
        """显示错误消息框。"""
        QMessageBox.critical(self, title, message)

    @pyqtSlot()
    def _on_refresh_data(self):
        """
        刷新数据，委托给控制器处理。
        """
        self.logger.info("刷新数据功能被触发，将委托给控制器。")
        if hasattr(self, 'controller'):
            # 控制器需要一个 `reload_current_data` 或类似的方法
            if hasattr(self.controller, 'reload_current_data'):
                 self.controller.reload_current_data()
            else: # Fallback to navigating to the current path
                 current_path = self.state_service.get_current_navigation_path()
                 if current_path:
                    self.controller.navigate_to_path(current_path)
                 else:
                    self.logger.warning("无法刷新，因为没有当前导航路径。")
        else:
            self.logger.warning("控制器未初始化，无法刷新数据。")

    def showEvent(self, event):
        """窗口显示时触发的事件，用于初始化加载。"""
        super().showEvent(event)
        # 强制更新一次响应式布局
        if hasattr(self, 'responsive_manager'):
            QTimer.singleShot(100, self.responsive_manager.force_update)

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 保存用户偏好设置
            if hasattr(self, 'user_preferences'):
                window_geometry = self.saveGeometry()
                # 将QByteArray转换为可序列化的格式
                if hasattr(window_geometry, 'data'):
                    # 转换为base64字符串
                    import base64
                    geometry_data = base64.b64encode(window_geometry.data()).decode('utf-8')
                    self.user_preferences.set_preference('window_geometry', geometry_data)
                    self.logger.info("已保存窗口几何设置")

            self.logger.info("主窗口正常关闭")
            event.accept()

        except Exception as e:
            self.logger.error(f"窗口关闭事件处理失败: {e}")
            event.accept()  # 即使有错误也允许关闭

    def _get_modern_styles(self) -> str:
        """获取现代化的QSS样式表。"""
        return """
            /* 主窗口样式 */
            QMainWindow {
                background-color: #FAFAFA;
                color: #212121;
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 14px;
            }
            
            /* Material Design按钮样式 */
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
            }
            
            QPushButton:hover {
                background-color: #1976D2;
            }
            
            QPushButton:pressed {
                background-color: #1565C0;
            }
            
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
            }
            
            /* 输入框样式 */
            QLineEdit, QTextEdit, QComboBox {
                border: 2px solid #E0E0E0;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #2196F3;
            }
            
            /* 分割器样式 */
            QSplitter::handle {
                background-color: #E0E0E0;
                width: 3px;
                height: 3px;
            }
            
            QSplitter::handle:hover {
                background-color: #BDBDBD;
            }
            
            /* 状态栏样式 */
            QStatusBar {
                background-color: #F5F5F5;
                border-top: 1px solid #E0E0E0;
                padding: 4px;
            }
            
            /* 菜单栏样式 */
            QMenuBar {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }
            
            QMenuBar::item:selected {
                background-color: #1976D2;
            }
            
            /* 工具栏样式 */
            QToolBar {
                background-color: #F5F5F5;
                border: 1px solid #E0E0E0;
                spacing: 2px;
                padding: 4px;
            }
            
            QToolButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                padding: 6px;
            }
            
            QToolButton:hover {
                background-color: #E3F2FD;
            }
            
            QToolButton:pressed {
                background-color: #BBDEFB;
            }
        """ 

    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)
        self.window_resized.emit(event.size())
    
    # ===================== 增强版表头管理器集成方法 =====================
    
    def _register_all_tables_to_header_manager(self):
        """
        注册所有表格组件到表头管理器
        
        这个方法会扫描窗口中所有的QTableWidget组件并注册到表头管理器
        """
        try:
            # 查找所有QTableWidget组件
            all_tables = self.findChildren(QTableWidget)
            registered_count = 0
            
            for i, table in enumerate(all_tables):
                # 生成唯一的表格ID
                table_id = f"table_{i}_{id(table)}"
                
                # 尝试获取更有意义的ID
                if hasattr(table, 'objectName') and table.objectName():
                    table_id = f"{table.objectName()}_{id(table)}"
                elif hasattr(table, 'table_name'):
                    table_id = f"{table.table_name}_{id(table)}"
                
                # 注册到表头管理器
                if self.header_manager.register_table(table_id, table):
                    registered_count += 1
                    self.logger.debug(f"表格 {table_id} 已注册到表头管理器")
            
            self.logger.info(f"已注册 {registered_count} 个表格到表头管理器")
            
        except Exception as e:
            self.logger.error(f"注册表格到表头管理器失败: {e}")
    
    def _conditional_header_cleanup(self):
        """
        有条件的表头清理（仅在真正需要时执行）
        """
        try:
            # 先检测是否有重影问题
            self._register_all_tables_to_header_manager()
            shadow_tables = self.header_manager.auto_detect_and_fix_shadows()
            
            if shadow_tables:
                self.logger.info(f"检测到表头重影，已自动修复: {list(shadow_tables.keys())}")
                if hasattr(self, 'main_workspace'):
                    self.main_workspace._update_status_label(
                        f"已修复 {len(shadow_tables)} 个表格的表头重影", "info"
                    )
            else:
                self.logger.debug("未检测到表头重影问题，跳过清理")
            
        except Exception as e:
            self.logger.error(f"条件表头清理失败: {e}")
            # 回退到原有的清理方法
            try:
                if hasattr(self, 'menu_bar_manager'):
                    self.menu_bar_manager._clear_table_header_cache()
            except Exception as fallback_error:
                self.logger.error(f"回退清理方法也失败: {fallback_error}")
    
    def _enhanced_header_cleanup(self, table_id: Optional[str] = None, delay_ms: int = 100):
        """
        增强版表头清理（已废弃，使用_conditional_header_cleanup代替）
        
        Args:
            table_id: 指定表格ID，None表示清理所有
            delay_ms: 延迟清理时间（毫秒）
        """
        self.logger.warning("_enhanced_header_cleanup已废弃，改用_conditional_header_cleanup")
        self._conditional_header_cleanup()
    
    @pyqtSlot(str)
    def _on_header_cleaned(self, table_id: str):
        """
        表头清理完成信号处理
        
        Args:
            table_id: 完成清理的表格ID
        """
        try:
            self.logger.debug(f"表格 {table_id} 表头清理完成")
            
            # 更新状态显示
            if hasattr(self, 'main_workspace'):
                self.main_workspace._update_status_label(
                    f"表头重影修复完成", "info"
                )
            
        except Exception as e:
            self.logger.error(f"处理表头清理完成信号失败: {e}")
    
    @pyqtSlot(str, list)
    def _on_header_shadow_detected(self, table_id: str, duplicate_labels: List[str]):
        """
        表头重影检测信号处理（记录信息，不自动触发修复）
        
        Args:
            table_id: 检测到重影的表格ID
            duplicate_labels: 重复的标签列表
        """
        try:
            self.logger.warning(f"检测到表格 {table_id} 表头重影: {duplicate_labels}")
            
            # 仅显示信息，不自动触发修复
            if hasattr(self, 'main_workspace'):
                self.main_workspace._update_status_label(
                    f"检测到表头重影问题", "warning"
                )
            
        except Exception as e:
            self.logger.error(f"处理表头重影检测信号失败: {e}")
    
    def force_header_cleanup_all_tables(self):
        """
        强制清理所有表格的表头重影
        
        这是一个公共方法，可以在需要时手动调用
        """
        try:
            self.logger.info("开始强制清理所有表格的表头重影")
            
            # 先检测重影
            shadow_tables = self.header_manager.auto_detect_and_fix_shadows()
            
            if shadow_tables:
                self.logger.warning(f"检测到 {len(shadow_tables)} 个表格存在重影，已自动修复")
                
                # 显示修复结果
                if hasattr(self, 'main_workspace'):
                    self.main_workspace._update_status_label(
                        f"已修复 {len(shadow_tables)} 个表格的表头重影问题", "info"
                    )
            else:
                self.logger.info("未检测到表头重影问题")
                
                # 显示正常状态
                if hasattr(self, 'main_workspace'):
                    self.main_workspace._update_status_label(
                        "表头状态正常，无重影问题", "info"
                    )
            
            # 执行一次全面清理
            self._enhanced_header_cleanup(None, 100)
            
        except Exception as e:
            self.logger.error(f"强制清理表头重影失败: {e}")
            self._show_error_message("表头清理失败", f"清理过程中发生错误: {e}")
    
    def get_header_manager_statistics(self) -> Dict[str, Any]:
        """
        获取表头管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if hasattr(self, 'header_manager'):
                return self.header_manager.get_statistics()
            else:
                return {'error': '表头管理器未初始化'}
        except Exception as e:
            self.logger.error(f"获取表头管理器统计信息失败: {e}")
            return {'error': str(e)}
    
    def validate_all_table_headers(self) -> Dict[str, Dict[str, Any]]:
        """
        验证所有表格的表头状态
        
        Returns:
            Dict[str, Dict[str, Any]]: 验证结果，键为表格ID，值为验证结果
        """
        validation_results = {}
        
        try:
            # 确保表格已注册
            self._register_all_tables_to_header_manager()
            
            # 验证每个注册的表格
            for table_id in self.header_manager.registered_tables.keys():
                result = self.header_manager.validate_header_state(table_id)
                validation_results[table_id] = result
            
            # 统计验证结果
            valid_count = sum(1 for r in validation_results.values() if r.get('is_valid', False))
            total_count = len(validation_results)
            
            self.logger.info(f"表头验证完成: {valid_count}/{total_count} 个表格状态正常")
            
        except Exception as e:
            self.logger.error(f"验证表头状态失败: {e}")
            validation_results['error'] = str(e)
        
        return validation_results 