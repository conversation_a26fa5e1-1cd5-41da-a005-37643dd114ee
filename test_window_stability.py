#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口稳定性测试程序
逐步测试窗口组件，找出导致闪退的原因
"""

import sys
import os
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_window():
    """测试基本窗口是否能稳定显示"""
    try:
        print("测试1: 基本PyQt5窗口...")
        
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt, QTimer
        
        app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("稳定性测试 - 基本窗口")
        window.setGeometry(100, 100, 600, 400)
        
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        label = QLabel("基本窗口测试\n窗口应该保持显示不闪退")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        window.show()
        print("✓ 基本窗口显示成功")
        
        # 设置5秒后自动关闭
        QTimer.singleShot(5000, app.quit)
        
        exit_code = app.exec_()
        print(f"基本窗口测试完成，退出代码: {exit_code}")
        return True
        
    except Exception as e:
        print(f"基本窗口测试失败: {e}")
        traceback.print_exc()
        return False

def test_with_managers():
    """测试包含管理器的窗口"""
    try:
        print("\n测试2: 包含核心管理器的窗口...")
        
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt, QTimer
        
        # 导入核心管理器
        from src.modules.system_config.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        app = QApplication(sys.argv)
        
        # 初始化管理器
        print("初始化配置管理器...")
        config_manager = ConfigManager()
        print("✓ 配置管理器初始化成功")
        
        print("初始化数据库管理器...")
        db_manager = DatabaseManager(config_manager=config_manager)
        print("✓ 数据库管理器初始化成功")
        
        print("初始化动态表管理器...")
        dynamic_table_manager = DynamicTableManager(db_manager=db_manager, config_manager=config_manager)
        print("✓ 动态表管理器初始化成功")
        
        window = QMainWindow()
        window.setWindowTitle("稳定性测试 - 包含管理器")
        window.setGeometry(100, 100, 600, 400)
        
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        label = QLabel("管理器测试\n所有核心管理器已初始化\n窗口应该保持显示")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        window.show()
        print("✓ 包含管理器的窗口显示成功")
        
        # 设置5秒后自动关闭
        QTimer.singleShot(5000, app.quit)
        
        exit_code = app.exec_()
        print(f"管理器窗口测试完成，退出代码: {exit_code}")
        return True
        
    except Exception as e:
        print(f"管理器窗口测试失败: {e}")
        traceback.print_exc()
        return False

def test_prototype_window_minimal():
    """测试最小化的PrototypeMainWindow"""
    try:
        print("\n测试3: 最小化PrototypeMainWindow...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # 导入核心管理器
        from src.modules.system_config.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        app = QApplication(sys.argv)
        
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        dynamic_table_manager = DynamicTableManager(db_manager=db_manager, config_manager=config_manager)
        
        print("尝试创建PrototypeMainWindow...")
        
        # 导入PrototypeMainWindow
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        
        window = PrototypeMainWindow(
            config_manager=config_manager,
            db_manager=db_manager,
            dynamic_table_manager=dynamic_table_manager
        )
        
        print("✓ PrototypeMainWindow创建成功")
        
        window.show()
        print("✓ PrototypeMainWindow显示成功")
        
        # 设置10秒后自动关闭，给更多时间观察
        QTimer.singleShot(10000, app.quit)
        
        exit_code = app.exec_()
        print(f"PrototypeMainWindow测试完成，退出代码: {exit_code}")
        return True
        
    except Exception as e:
        print(f"PrototypeMainWindow测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("窗口稳定性测试程序")
    print("=" * 60)
    
    # 测试1: 基本窗口
    if not test_basic_window():
        print("基本窗口测试失败，停止后续测试")
        sys.exit(1)
    
    # 测试2: 包含管理器的窗口
    if not test_with_managers():
        print("管理器窗口测试失败，停止后续测试")
        sys.exit(1)
    
    # 测试3: PrototypeMainWindow
    if not test_prototype_window_minimal():
        print("PrototypeMainWindow测试失败")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("所有测试通过！窗口可以稳定显示")
    print("=" * 60)
