#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基于数据库字段的映射生成功能

验证AutoFieldMappingGenerator新增的generate_mapping_from_db_fields方法
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator
from src.utils.log_config import setup_logger

def test_db_field_mapping_basic():
    """测试基本的数据库字段映射功能"""
    logger = setup_logger("test_db_field_mapping")
    logger.info("开始测试基于数据库字段的映射生成...")
    
    try:
        # 初始化生成器
        generator = AutoFieldMappingGenerator()
        
        # 模拟2016年数据的真实数据库字段（基于之前修复的经验）
        db_fields_2016 = [
            "id", "employee_id", "employee_name", "id_card", "department", 
            "position", "basic_salary", "performance_bonus", "overtime_pay", 
            "allowance", "deduction", "total_salary", "month", "year", 
            "created_at", "updated_at"
        ]
        
        # 生成映射
        table_name = "salary_data_2016_01_test_new"
        mapping_result = generator.generate_mapping_from_db_fields(
            db_fields=db_fields_2016,
            table_name=table_name
        )
        
        # 验证结果
        logger.info("=== 映射生成结果 ===")
        for db_field, display_name in mapping_result.items():
            logger.info(f"{db_field} -> {display_name}")
        
        # 验证关键字段是否正确映射
        expected_mappings = {
            "employee_id": "工号",
            "employee_name": "姓名",
            "department": "部门",
            "total_salary": "应发合计",
            "basic_salary": "基本工资"
        }
        
        success_count = 0
        for db_field, expected_display in expected_mappings.items():
            if db_field in mapping_result:
                actual_display = mapping_result[db_field]
                if actual_display == expected_display:
                    logger.info(f"✓ {db_field} 映射正确: {actual_display}")
                    success_count += 1
                else:
                    logger.warning(f"✗ {db_field} 映射不符合预期: 期望 {expected_display}, 实际 {actual_display}")
            else:
                logger.error(f"✗ {db_field} 未找到映射")
        
        # 获取映射统计
        stats = generator.get_mapping_statistics()
        logger.info(f"=== 映射统计 ===")
        logger.info(f"总生成数: {stats['total_generated']}")
        logger.info(f"精确匹配: {stats['exact_matches']}")
        logger.info(f"模糊匹配: {stats['fuzzy_matches']}")
        logger.info(f"DB字段映射: {stats['db_field_mapped']}")
        logger.info(f"保持原名: {stats['original_kept']}")
        
        # 成功率计算
        success_rate = success_count / len(expected_mappings) * 100
        logger.info(f"=== 测试结果 ===")
        logger.info(f"关键字段映射成功率: {success_rate:.1f}% ({success_count}/{len(expected_mappings)})")
        
        if success_rate >= 80:
            logger.info("✓ 基础测试通过！")
            return True
        else:
            logger.error("✗ 基础测试失败！")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_excel_hints():
    """测试带Excel提示的映射生成"""
    logger = setup_logger("test_excel_hints")
    logger.info("开始测试带Excel提示的映射生成...")
    
    try:
        generator = AutoFieldMappingGenerator()
        
        # 模拟实际场景：Excel列名和数据库字段名
        excel_columns = [
            "工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", 
            "津贴", "应发工资", "2025公积金"
        ]
        
        db_fields = [
            "employee_id", "employee_name", "department", "position_salary", 
            "grade_salary", "allowance", "total_salary", "housing_fund"
        ]
        
        # 生成映射
        mapping_result = generator.generate_mapping_from_db_fields(
            db_fields=db_fields,
            table_name="salary_data_2025_05_with_excel_hints",
            original_excel_columns=excel_columns
        )
        
        logger.info("=== 带Excel提示的映射结果 ===")
        for db_field, display_name in mapping_result.items():
            logger.info(f"{db_field} -> {display_name}")
        
        # 验证是否利用了Excel列名信息
        chinese_count = sum(1 for name in mapping_result.values() 
                          if generator._contains_chinese(name))
        
        logger.info(f"=== Excel提示效果验证 ===")
        logger.info(f"中文显示名数量: {chinese_count}/{len(mapping_result)}")
        
        if chinese_count >= len(mapping_result) * 0.8:  # 80%以上是中文
            logger.info("✓ Excel提示功能正常工作！")
            return True
        else:
            logger.warning("✗ Excel提示功能可能需要改进")
            return False
            
    except Exception as e:
        logger.error(f"Excel提示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_intelligent_parsing():
    """测试智能字段解析功能"""
    logger = setup_logger("test_intelligent_parsing")
    logger.info("开始测试智能字段解析...")
    
    try:
        generator = AutoFieldMappingGenerator()
        
        # 测试各种英文字段名的智能解析
        test_fields = [
            "user_name", "staff_salary", "bonus_amount", "dept_name",
            "contact_phone", "email_address", "total_amount", "payment_date"
        ]
        
        mapping_result = generator.generate_mapping_from_db_fields(
            db_fields=test_fields,
            table_name="test_intelligent_parsing"
        )
        
        logger.info("=== 智能解析结果 ===")
        for db_field, display_name in mapping_result.items():
            logger.info(f"{db_field} -> {display_name}")
        
        # 检查是否有合理的中文映射
        reasonable_mappings = 0
        for db_field, display_name in mapping_result.items():
            if generator._contains_chinese(display_name) or "字段_" not in display_name:
                reasonable_mappings += 1
        
        logger.info(f"=== 智能解析效果 ===")
        logger.info(f"合理映射数量: {reasonable_mappings}/{len(test_fields)}")
        
        if reasonable_mappings >= len(test_fields) * 0.6:  # 60%以上合理
            logger.info("✓ 智能解析功能基本正常！")
            return True
        else:
            logger.warning("✗ 智能解析功能需要改进")
            return False
            
    except Exception as e:
        logger.error(f"智能解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("=" * 80)
    print("数据库字段映射生成器功能测试")
    print("=" * 80)
    
    test_results = []
    
    # 运行基础测试
    print("\n1. 基础功能测试...")
    test_results.append(test_db_field_mapping_basic())
    
    # 运行Excel提示测试
    print("\n2. Excel提示功能测试...")
    test_results.append(test_with_excel_hints())
    
    # 运行智能解析测试
    print("\n3. 智能解析功能测试...")
    test_results.append(test_intelligent_parsing())
    
    # 总结测试结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print(f"通过测试: {passed_tests}/{total_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！AutoFieldMappingGenerator增强功能正常工作")
        return True
    else:
        print("⚠️  部分测试未通过，需要进一步优化")
        return False

if __name__ == "__main__":
    main() 