"""
自动字段映射生成器

功能说明:
- 在数据导入时自动为每个表生成完整的字段映射配置
- 智能映射算法 + 用户自定义规则
- 多策略映射生成：精确匹配、模糊匹配、保持原始名称
- 映射质量评估和建议生成
- **新增：基于数据库字段的智能映射生成**

功能函数:
- generate_mapping(): 生成字段映射配置
- generate_mapping_from_db_fields(): 基于数据库字段生成映射配置（新增）
- assess_mapping_quality(): 评估映射质量
- update_mapping_rules(): 更新映射规则
- export_mapping_template(): 导出映射模板

创建时间: 2025-06-23
更新时间: 2025-06-26 - 新增数据库字段映射功能
作者: 开发团队
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
from difflib import SequenceMatcher

from src.utils.log_config import setup_logger


class AutoFieldMappingGenerator:
    """自动字段映射生成器
    
    提供智能的字段映射自动生成功能
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化自动映射生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.logger = setup_logger(__name__)
        
        # 初始化配置
        self.config_path = config_path or "state/data/field_mappings.json"
        self.mapping_rules_path = "state/data/auto_mapping_rules.json"
        
        # 加载映射规则
        self.standard_mappings = self._load_standard_mappings()
        self.user_mapping_rules = self._load_user_mapping_rules()
        
        # 映射质量统计
        self.mapping_stats = {
            "total_generated": 0,
            "exact_matches": 0,
            "fuzzy_matches": 0,
            "original_kept": 0,
            "db_field_mapped": 0  # 新增：数据库字段映射统计
        }
        
        self.logger.info("自动字段映射生成器初始化完成")
    
    def generate_mapping(self, excel_columns: List[str], table_name: str) -> Dict[str, str]:
        """
        多策略字段映射生成算法
        
        策略优先级:
        1. 精确匹配已知字段映射规则
        2. 模糊匹配常用字段模式
        3. 保持原始列名作为显示名
        4. 用户自定义映射规则
        
        Args:
            excel_columns: Excel列名列表
            table_name: 表名
            
        Returns:
            Dict[str, str]: 生成的字段映射配置
        """
        mapping_result = {}
        generation_log = []
        
        self.logger.info(f"开始为表 '{table_name}' 生成字段映射，共 {len(excel_columns)} 个字段")
        
        for excel_col in excel_columns:
            # 清理字段名
            clean_col = self._clean_field_name(excel_col)
            
            # 策略1: 精确匹配
            exact_match = self._exact_match_mapping(clean_col)
            if exact_match:
                mapping_result[excel_col] = exact_match
                generation_log.append(f"精确匹配: {excel_col} -> {exact_match}")
                self.mapping_stats["exact_matches"] += 1
                continue
            
            # 策略2: 模糊匹配
            fuzzy_match = self._fuzzy_match_mapping(clean_col)
            if fuzzy_match:
                mapping_result[excel_col] = fuzzy_match
                generation_log.append(f"模糊匹配: {excel_col} -> {fuzzy_match}")
                self.mapping_stats["fuzzy_matches"] += 1
                continue
            
            # 策略3: 用户自定义规则匹配
            user_match = self._user_rule_mapping(clean_col, table_name)
            if user_match:
                mapping_result[excel_col] = user_match
                generation_log.append(f"用户规则匹配: {excel_col} -> {user_match}")
                continue
            
            # 策略4: 中文表头特殊处理
            if self._contains_chinese(excel_col):
                # 如果是中文表头，直接使用原始表头作为显示名
                mapping_result[excel_col] = excel_col
                generation_log.append(f"中文表头保持: {excel_col} -> {excel_col}")
                self.mapping_stats["original_kept"] += 1
            else:
                # 策略5: 保持原始名称
                mapping_result[excel_col] = clean_col
                generation_log.append(f"保持原名: {excel_col} -> {clean_col}")
                self.mapping_stats["original_kept"] += 1
        
        self.mapping_stats["total_generated"] += len(excel_columns)
        
        # 记录生成日志
        self.logger.info(f"字段映射生成完成，详细信息:")
        for log_entry in generation_log:
            self.logger.debug(log_entry)
        
        return mapping_result
    
    def generate_mapping_from_db_fields(self, db_fields: List[str], table_name: str,
                                      db_field_descriptions: Optional[Dict[str, str]] = None,
                                      original_excel_columns: Optional[List[str]] = None) -> Dict[str, str]:
        """基于数据库字段生成映射配置
        
        这是解决新增数据导入后中文表头显示问题的核心方法。
        基于真实数据库字段结构，而不是Excel列名，生成准确的字段映射。
        
        Args:
            db_fields: 数据库实际字段名列表（英文字段名）
            table_name: 表名
            db_field_descriptions: 数据库字段描述（可选，用于提供映射线索）
            original_excel_columns: 原始Excel列名（可选，用于提供映射线索）
            
        Returns:
            Dict[str, str]: 数据库字段名 -> 中文显示名的映射
        """
        mapping_result = {}
        generation_log = []
        
        self.logger.info(f"开始为表 '{table_name}' 基于数据库字段生成映射，共 {len(db_fields)} 个字段")
        
        # 建立Excel列名与数据库字段的对应关系（如果有Excel列名的话）
        excel_db_correlation = {}
        if original_excel_columns:
            excel_db_correlation = self._correlate_excel_to_db_fields(original_excel_columns, db_fields)
            self.logger.debug(f"Excel列名与数据库字段对应关系: {excel_db_correlation}")
        
        for db_field in db_fields:
            # 清理字段名
            clean_db_field = self._clean_field_name(db_field)
            
            # 策略0: 基于数据库字段描述（注释）直接映射 (最高优先级)
            if db_field_descriptions and db_field in db_field_descriptions and db_field_descriptions[db_field]:
                mapped_name = db_field_descriptions[db_field].strip()
                if mapped_name:
                    mapping_result[db_field] = mapped_name
                    generation_log.append(f"DB描述映射: {db_field} -> {mapped_name}")
                    self.mapping_stats["exact_matches"] += 1
                    self.mapping_stats["db_field_mapped"] += 1
                    continue

            # 策略1: 基于标准数据库字段映射
            standard_match = self._map_standard_db_field(db_field)
            if standard_match:
                mapping_result[db_field] = standard_match
                generation_log.append(f"标准DB字段映射: {db_field} -> {standard_match}")
                self.mapping_stats["exact_matches"] += 1
                self.mapping_stats["db_field_mapped"] += 1
                continue
            
            # 策略2: 利用Excel列名信息进行辅助映射
            if original_excel_columns and db_field in excel_db_correlation:
                excel_col = excel_db_correlation[db_field]
                excel_mapping = self._generate_mapping_from_excel_hint(excel_col, db_field)
                if excel_mapping:
                    mapping_result[db_field] = excel_mapping
                    generation_log.append(f"Excel辅助映射: {db_field} ({excel_col}) -> {excel_mapping}")
                    self.mapping_stats["fuzzy_matches"] += 1
                    self.mapping_stats["db_field_mapped"] += 1
                    continue
            
            # 策略3: 智能字段名解析
            intelligent_match = self._intelligent_field_parsing(db_field)
            if intelligent_match:
                mapping_result[db_field] = intelligent_match
                generation_log.append(f"智能解析: {db_field} -> {intelligent_match}")
                self.mapping_stats["fuzzy_matches"] += 1
                self.mapping_stats["db_field_mapped"] += 1
                continue
            
            # 策略4: 用户自定义规则匹配
            user_match = self._user_rule_mapping(clean_db_field, table_name)
            if user_match:
                mapping_result[db_field] = user_match
                generation_log.append(f"用户规则映射: {db_field} -> {user_match}")
                self.mapping_stats["db_field_mapped"] += 1
                continue
            
            # 策略5: 保持原始字段名（兜底策略）
            fallback_name = self._generate_fallback_display_name(db_field)
            mapping_result[db_field] = fallback_name
            generation_log.append(f"兜底映射: {db_field} -> {fallback_name}")
            self.mapping_stats["original_kept"] += 1
        
        self.mapping_stats["total_generated"] += len(db_fields)
        
        # 记录生成日志
        self.logger.info(f"基于数据库字段的映射生成完成，详细信息:")
        for log_entry in generation_log:
            self.logger.debug(log_entry)
        
        # 生成映射质量报告
        quality_assessment = self.assess_mapping_quality(mapping_result)
        self.logger.info(f"映射质量评估: 覆盖率 {quality_assessment['coverage_rate']*100:.1f}%, "
                        f"中文映射 {quality_assessment['chinese_mapped']}/{quality_assessment['total_fields']}")
        
        return mapping_result
    
    def _clean_field_name(self, field_name: str) -> str:
        """清理字段名称
        
        Args:
            field_name: 原始字段名
            
        Returns:
            str: 清理后的字段名
        """
        if not field_name:
            return ""
        
        # 去除首尾空白
        cleaned = str(field_name).strip()
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', '', cleaned)
        
        # 移除特殊字符
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', cleaned)
        
        return cleaned
    
    def _exact_match_mapping(self, field_name: str) -> Optional[str]:
        """精确匹配映射
        
        Args:
            field_name: 字段名
            
        Returns:
            Optional[str]: 匹配的映射名，如果没有匹配返回None
        """
        # 检查标准映射中的精确匹配
        for pattern_info in self.standard_mappings.values():
            if isinstance(pattern_info, dict):
                # 检查aliases中的精确匹配
                aliases = pattern_info.get("aliases", [])
                if field_name in aliases or field_name.lower() in [alias.lower() for alias in aliases]:
                    return pattern_info.get("display_name", field_name)
                
                # 检查正则表达式精确匹配
                regex_pattern = pattern_info.get("regex", "")
                if regex_pattern and re.fullmatch(regex_pattern, field_name, re.IGNORECASE):
                    return pattern_info.get("display_name", field_name)
        
        return None
    
    def _fuzzy_match_mapping(self, field_name: str) -> Optional[str]:
        """模糊匹配映射
        
        Args:
            field_name: 字段名
            
        Returns:
            Optional[str]: 匹配的映射名，如果没有匹配返回None
        """
        best_match = None
        best_score = 0.0
        min_similarity = 0.7  # 最小相似度阈值
        
        for pattern_info in self.standard_mappings.values():
            if isinstance(pattern_info, dict):
                # 与display_name的相似度匹配
                display_name = pattern_info.get("display_name", "")
                similarity = self._calculate_similarity(field_name, display_name)
                
                if similarity > best_score and similarity >= min_similarity:
                    best_score = similarity
                    best_match = display_name
                
                # 与aliases的相似度匹配
                aliases = pattern_info.get("aliases", [])
                for alias in aliases:
                    similarity = self._calculate_similarity(field_name, alias)
                    if similarity > best_score and similarity >= min_similarity:
                        best_score = similarity
                        best_match = display_name
        
        return best_match
    
    def _user_rule_mapping(self, field_name: str, table_name: str) -> Optional[str]:
        """用户自定义规则映射
        
        Args:
            field_name: 字段名
            table_name: 表名
            
        Returns:
            Optional[str]: 匹配的映射名，如果没有匹配返回None
        """
        # 检查全局用户规则
        global_patterns = self.user_mapping_rules.get("global_patterns", {})
        if field_name in global_patterns:
            return global_patterns[field_name]
        
        # 检查表特定规则
        table_patterns = self.user_mapping_rules.get("table_specific_patterns", {})
        for pattern, mappings in table_patterns.items():
            # 支持通配符匹配
            if self._match_table_pattern(table_name, pattern):
                if field_name in mappings:
                    return mappings[field_name]
        
        return None
    
    def _match_table_pattern(self, table_name: str, pattern: str) -> bool:
        """匹配表名模式（支持通配符）
        
        Args:
            table_name: 表名
            pattern: 模式（支持*通配符）
            
        Returns:
            bool: 是否匹配
        """
        # 转换通配符为正则表达式
        regex_pattern = pattern.replace("*", ".*")
        return bool(re.match(regex_pattern, table_name, re.IGNORECASE))
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """计算两个字符串的相似度
        
        Args:
            str1: 字符串1
            str2: 字符串2
            
        Returns:
            float: 相似度分数 (0.0-1.0)
        """
        if not str1 or not str2:
            return 0.0
        
        return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()
    
    def _load_standard_mappings(self) -> Dict[str, Any]:
        """加载标准映射规则库
        
        Returns:
            Dict[str, Any]: 标准映射规则
        """
        return {
            # 基础信息字段
            "employee_id": {
                "aliases": ["工号", "员工号", "职工编号", "编号"],
                "display_name": "工号"
            },
            "employee_name": {
                "aliases": ["姓名", "员工姓名", "职工姓名", "名字"],
                "display_name": "姓名"
            },
            "department": {
                "aliases": ["部门", "所属部门", "单位", "科室", "部门名称"],
                "display_name": "部门"
            },
            "position": {
                "aliases": ["职位", "岗位", "职务"],
                "display_name": "职位"
            },
            
            # 工资字段
            "basic_salary": {
                "aliases": ["基本工资", "基础工资", "基本薪资"],
                "display_name": "基本工资"
            },
            "position_salary": {
                "aliases": ["岗位工资", "职位工资", "岗位薪资"],
                "display_name": "岗位工资"
            },
            "grade_salary": {
                "aliases": ["薪级工资", "等级工资", "薪级薪资"],
                "display_name": "薪级工资"
            },
            "performance": {
                "aliases": ["绩效", "奖金", "绩效工资", "绩效奖金"],
                "display_name": "绩效工资"
            },
            "allowance": {
                "aliases": ["津贴", "补贴", "津贴补贴"],
                "display_name": "津贴补贴"
            },
            "total_salary": {
                "aliases": ["应发工资", "应发合计", "工资合计", "总工资"],
                "display_name": "应发合计"
            },
            "social_insurance": {
                "aliases": ["五险一金", "社保", "公积金"],
                "display_name": "五险一金个人"
            }
        }
    
    def _load_user_mapping_rules(self) -> Dict[str, Any]:
        """加载用户自定义映射规则
        
        Returns:
            Dict[str, Any]: 用户映射规则
        """
        try:
            if Path(self.mapping_rules_path).exists():
                with open(self.mapping_rules_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载用户映射规则失败: {e}")
        
        # 返回默认用户规则
        return {
            "global_patterns": {
                "薪级工资": "薪级工资",
                "岗位工资": "岗位工资",
                "应发工资": "应发合计"
            },
            "table_specific_patterns": {
                "salary_data_*": {
                    "2025年岗位工资": "岗位工资",
                    "2025年薪级工资": "薪级工资",
                    "2025年奖励性绩效预发": "绩效工资"
                }
            }
        }
    
    def assess_mapping_quality(self, mapping: Dict[str, str]) -> Dict[str, Any]:
        """评估映射质量"""
        if not mapping:
            return {
                "total_fields": 0,
                "mapped_fields": 0,
                "unmapped_fields": 0,
                "coverage_rate": 0.0,
                "chinese_mapped": 0,
                "suggestions": ["映射为空，无质量评估。"]
            }

        total_fields = len(mapping)
        mapped_fields = sum(1 for k, v in mapping.items() if k != v)
        chinese_mapped = sum(1 for v in mapping.values() if self._contains_chinese(v))
        coverage_rate = chinese_mapped / total_fields
        
        return {
            "total_fields": total_fields,
            "mapped_fields": mapped_fields,
            "unmapped_fields": total_fields - mapped_fields,
            "coverage_rate": round(coverage_rate, 3),
            "chinese_mapped": chinese_mapped,
            "suggestions": []
        }
    
    def _contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符
        
        Args:
            text: 文本
            
        Returns:
            bool: 是否包含中文
        """
        return bool(re.search(r'[\u4e00-\u9fff]', text))
    
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """获取映射生成统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.mapping_stats.copy()
    
    def _map_standard_db_field(self, db_field: str) -> Optional[str]:
        """映射标准数据库字段
        
        基于我们在2016年数据修复中总结的经验，直接映射常见的英文数据库字段。
        
        Args:
            db_field: 数据库字段名
            
        Returns:
            Optional[str]: 对应的中文显示名
        """
        # 标准数据库字段映射表（基于2016年修复经验）
        standard_db_mappings = {
            # 基础字段
            "id": "ID",
            "employee_id": "工号",
            "employee_name": "姓名", 
            "id_card": "身份证号",
            "department": "部门",
            "position": "职位",
            
            # 工资字段
            "basic_salary": "基本工资",
            "position_salary": "岗位工资",
            "grade_salary": "薪级工资",
            "performance_bonus": "绩效工资",
            "performance_salary": "绩效工资",
            "overtime_pay": "加班费",
            "allowance": "津贴补贴",
            "subsidy": "津贴补贴",
            "deduction": "扣除项",
            "total_salary": "应发合计",
            "gross_salary": "应发合计",
            "net_salary": "实发工资",
            
            # 社保字段
            "social_insurance": "五险一金个人",
            "pension_insurance": "养老保险",
            "medical_insurance": "医疗保险",
            "unemployment_insurance": "失业保险",
            "work_injury_insurance": "工伤保险",
            "maternity_insurance": "生育保险",
            "housing_fund": "住房公积金",
            
            # 时间字段
            "month": "月份",
            "year": "年份",
            "created_at": "创建时间",
            "updated_at": "更新时间",
            "date": "日期",
            
            # 其他常见字段
            "phone": "手机号",
            "email": "邮箱",
            "address": "地址",
            "remark": "备注",
            "status": "状态"
        }
        
        # 直接匹配
        if db_field.lower() in standard_db_mappings:
            return standard_db_mappings[db_field.lower()]
        
        # 尝试模糊匹配
        for std_field, display_name in standard_db_mappings.items():
            if std_field in db_field.lower() or db_field.lower() in std_field:
                self.logger.debug(f"模糊匹配标准字段: {db_field} -> {display_name}")
                return display_name
        
        return None
    
    def _correlate_excel_to_db_fields(self, excel_columns: List[str], db_fields: List[str]) -> Dict[str, str]:
        """建立Excel列名与数据库字段的对应关系
        
        Args:
            excel_columns: Excel列名列表
            db_fields: 数据库字段名列表
            
        Returns:
            Dict[str, str]: db_field -> excel_column 的对应关系
        """
        correlation = {}
        
        # 简单情况：数量相同且顺序可能对应
        if len(excel_columns) == len(db_fields):
            for db_field, excel_col in zip(db_fields, excel_columns):
                correlation[db_field] = excel_col
        
        # TODO: 在后续版本中可以实现更复杂的对应关系推断
        
        return correlation
    
    def _generate_mapping_from_excel_hint(self, excel_col: str, db_field: str) -> Optional[str]:
        """基于Excel列名提示生成数据库字段的映射
        
        Args:
            excel_col: Excel列名
            db_field: 数据库字段名
            
        Returns:
            Optional[str]: 生成的中文显示名
        """
        # 如果Excel列名是中文，直接使用
        if self._contains_chinese(excel_col):
            return excel_col
        
        # 尝试通过Excel列名查找标准映射
        excel_mapping = self._exact_match_mapping(excel_col)
        if excel_mapping:
            return excel_mapping
        
        # 尝试模糊匹配
        excel_fuzzy = self._fuzzy_match_mapping(excel_col)
        if excel_fuzzy:
            return excel_fuzzy
        
        return None
    
    def _intelligent_field_parsing(self, db_field: str) -> Optional[str]:
        """智能字段名解析
        
        基于字段名的语义信息，智能推断中文显示名
        
        Args:
            db_field: 数据库字段名
            
        Returns:
            Optional[str]: 推断的中文显示名
        """
        field_lower = db_field.lower()
        
        # 常见的英文词汇映射
        word_mappings = {
            "name": "姓名",
            "salary": "工资", 
            "pay": "工资",
            "bonus": "奖金",
            "allowance": "津贴",
            "department": "部门",
            "position": "职位",
            "date": "日期",
            "time": "时间",
            "id": "编号",
            "number": "编号",
            "phone": "电话",
            "mobile": "手机",
            "email": "邮箱",
            "address": "地址",
            "total": "合计",
            "sum": "合计",
            "amount": "金额"
        }
        
        # 检查字段名中是否包含这些词汇
        for eng_word, chinese_word in word_mappings.items():
            if eng_word in field_lower:
                # 可以进一步优化，比如组合多个词汇
                return chinese_word
        
        return None
    
    def _generate_fallback_display_name(self, db_field: str) -> str:
        """生成兜底的显示名称
        
        Args:
            db_field: 数据库字段名
            
        Returns:
            str: 兜底显示名称
        """
        # 简单的转换：去除下划线，首字母大写
        fallback = db_field.replace("_", " ").title()
        
        # 如果还是很不友好，加个前缀表明这是字段名
        if len(fallback) > 20 or not fallback.replace(" ", "").isalnum():
            fallback = f"字段_{db_field}"
        
        return fallback 