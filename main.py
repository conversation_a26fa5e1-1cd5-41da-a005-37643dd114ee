#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月度工资异动处理系统 - 主应用入口

本文件是系统的主入口，负责启动应用程序和初始化各个模块。

功能:
1. 应用程序初始化
2. 异常处理和日志记录
3. 系统环境检查
4. 主窗口启动
"""

import sys
import os
import traceback
from pathlib import Path

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置应用程序信息
APP_NAME = "月度工资异动处理系统"
APP_VERSION = "2.0.0-refactored"
APP_AUTHOR = "月度工资异动处理系统开发团队"

def setup_environment():
    """设置应用程序环境"""
    try:
        # 创建必要的目录
        directories = [
            'logs',
            'data',
            'output',
            'temp',
            'backup'
        ]
        
        for directory in directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True)
        
        # 设置环境变量
        os.environ['APP_NAME'] = APP_NAME
        os.environ['APP_VERSION'] = APP_VERSION
        os.environ['APP_ROOT'] = str(project_root)
        
        return True
        
    except Exception as e:
        print(f"环境设置失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    try:
        print("正在检查系统依赖...")

        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            print(f"当前版本: {sys.version}")
            return False

        print(f"✓ Python版本检查通过: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

        # 检查必要的包
        required_packages = [
            ('PyQt5', 'PyQt5'),
            ('pandas', 'pandas'),
            ('openpyxl', 'openpyxl'),
            ('python-docx', 'docx'),
            ('sqlite3', 'sqlite3')
        ]

        missing_packages = []
        for package_name, import_name in required_packages:
            try:
                if import_name == 'sqlite3':
                    import sqlite3
                elif import_name == 'PyQt5':
                    import PyQt5
                    # 额外检查PyQt5的关键模块
                    from PyQt5.QtWidgets import QApplication
                    from PyQt5.QtCore import Qt
                    from PyQt5.QtGui import QFont
                elif import_name == 'pandas':
                    import pandas
                elif import_name == 'openpyxl':
                    import openpyxl
                elif import_name == 'docx':
                    import docx
                print(f"✓ {package_name} 导入成功")
            except ImportError as e:
                print(f"✗ {package_name} 导入失败: {e}")
                missing_packages.append(package_name)

        if missing_packages:
            print(f"\n错误: 缺少以下必要的包: {', '.join(missing_packages)}")
            print("请运行以下命令安装缺失的包:")
            print("pip install -r requirements.txt")
            print("或者单独安装:")
            for pkg in missing_packages:
                print(f"pip install {pkg}")
            return False

        print("✓ 所有依赖包检查通过")
        return True

    except Exception as e:
        print(f"依赖检查失败: {e}")
        traceback.print_exc()
        return False

def check_required_files():
    """检查必要的配置文件和资源文件"""
    try:
        print("正在检查必要文件...")

        # 必要的目录
        required_dirs = [
            'logs',
            'data',
            'data/db',
            'output',
            'temp',
            'backup',
            'state',
            'state/user',
            'state/data',
            'state/sessions',
            'state/windows',
            'state/backups'
        ]

        # 检查并创建必要目录
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            if not dir_path.exists():
                print(f"创建缺失目录: {dir_name}")
                dir_path.mkdir(parents=True, exist_ok=True)
            else:
                print(f"✓ 目录存在: {dir_name}")

        # 必要的配置文件
        config_files = [
            ('config.json', 'src/modules/system_config/default_config.json'),
            ('user_preferences.json', None)  # 用户偏好文件可以不存在，会自动创建
        ]

        for config_file, default_source in config_files:
            config_path = project_root / config_file
            if not config_path.exists():
                if default_source and (project_root / default_source).exists():
                    print(f"从默认配置创建: {config_file}")
                    import shutil
                    shutil.copy2(project_root / default_source, config_path)
                elif config_file == 'config.json':
                    print(f"创建默认配置文件: {config_file}")
                    create_default_config(config_path)
                else:
                    print(f"注意: {config_file} 不存在，将在运行时自动创建")
            else:
                print(f"✓ 配置文件存在: {config_file}")

        print("✓ 必要文件检查完成")
        return True

    except Exception as e:
        print(f"文件检查失败: {e}")
        traceback.print_exc()
        return False

def create_default_config(config_path):
    """创建默认配置文件"""
    try:
        default_config = {
            "database": {
                "path": "data/db/salary_system.db",
                "backup_enabled": True,
                "backup_interval_hours": 24
            },
            "logging": {
                "level": "INFO",
                "file_enabled": True,
                "console_enabled": True,
                "max_file_size_mb": 10,
                "backup_count": 5
            },
            "ui": {
                "theme": "default",
                "font_family": "Microsoft YaHei",
                "font_size": 9,
                "window_state_save": True
            },
            "data_import": {
                "max_file_size_mb": 100,
                "supported_formats": [".xlsx", ".xls", ".csv"],
                "auto_backup": True
            },
            "performance": {
                "max_visible_rows": 1000,
                "pagination_size": 50,
                "cache_enabled": True,
                "max_cache_size_mb": 50
            }
        }

        import json
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)

        print(f"✓ 默认配置文件已创建: {config_path}")
        return True

    except Exception as e:
        print(f"创建默认配置文件失败: {e}")
        return False

def setup_exception_handler():
    """设置全局异常处理器"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理函数"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C正常退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录异常到日志文件
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # 写入日志文件
        log_file = project_root / 'logs' / 'error.log'
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"\n[{timestamp}] 未捕获的异常:\n{error_msg}\n")
        except Exception:
            pass  # 如果无法写入日志，忽略错误
        
        # 显示错误对话框
        try:
            from PyQt5.QtWidgets import QMessageBox, QApplication
            
            if QApplication.instance():
                msg_box = QMessageBox()
                msg_box.setIcon(QMessageBox.Critical)
                msg_box.setWindowTitle("系统错误")
                msg_box.setText("系统发生未处理的错误")
                msg_box.setDetailedText(error_msg)
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.exec_()
        except Exception:
            # 如果GUI不可用，打印到控制台
            print(f"系统错误: {error_msg}")
    
    # 设置异常处理器
    sys.excepthook = handle_exception

def create_application():
    """创建QApplication实例"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon, QFont
        
        # 设置高DPI支持 - 必须在QApplication创建之前设置
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName(APP_NAME)
        app.setApplicationVersion(APP_VERSION)
        app.setOrganizationName(APP_AUTHOR)
        
        # 设置应用程序图标
        icon_path = project_root / 'resources' / 'icons' / 'app_icon.png'
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置全局字体
        font = QFont("Microsoft YaHei", 9)
        app.setFont(font)
        
        # 设置样式
        app.setStyle('Fusion')
        
        return app
        
    except Exception as e:
        print(f"创建应用程序失败: {e}")
        return None

def create_main_window(config_manager, db_manager, dynamic_table_manager):
    """创建主窗口"""
    try:
        print("开始创建主窗口...")
        # 重构后，我们统一使用 PrototypeMainWindow
        print("正在调用PrototypeMainWindow构造函数...")
        main_window = PrototypeMainWindow(
            config_manager=config_manager,
            db_manager=db_manager,
            dynamic_table_manager=dynamic_table_manager
        )
        print("PrototypeMainWindow构造函数返回成功")

        print("主窗口创建完成，正在显示...")
        main_window.show()
        print("主窗口显示完成")
        return main_window

    except Exception as e:
        print(f"创建主窗口失败: {e}")
        traceback.print_exc()
        return None

def setup_app_logging():
    """设置日志系统"""
    try:
        from src.utils.log_config import setup_logger
        logger = setup_logger(__name__)
        logger.info(f"{APP_NAME} v{APP_VERSION} 启动")
        return logger
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        return None

def show_splash_screen(app):
    """显示启动画面"""
    try:
        from PyQt5.QtWidgets import QSplashScreen, QLabel
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QPixmap, QFont
        
        # 创建启动画面
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        
        # 添加启动信息
        splash.showMessage(
            f"{APP_NAME}\n版本 {APP_VERSION}\n正在启动...",
            Qt.AlignCenter | Qt.AlignBottom,
            Qt.black
        )
        
        splash.show()
        app.processEvents()
        
        return splash
        
    except Exception as e:
        print(f"启动画面创建失败: {e}")
        return None

def show_version():
    """显示版本信息"""
    print(f"""
{APP_NAME} v{APP_VERSION}

开发团队: {APP_AUTHOR}
Python版本: {sys.version}
系统平台: {sys.platform}

项目目录: {project_root}
    """)

def show_help():
    """显示帮助信息"""
    print(f"""
{APP_NAME} v{APP_VERSION}

用法:
    python main.py [选项]

选项:
    -h, --help      显示此帮助信息
    -v, --version   显示版本信息
    --test          运行测试
    --check         检查系统环境和依赖

示例:
    python main.py              # 正常启动程序
    python main.py --test       # 运行测试
    python main.py --check      # 检查环境
    """)

if __name__ == "__main__":
    print(f"正在启动 {APP_NAME} v{APP_VERSION}...")
    print("=" * 50)

    # 1. 检查系统依赖
    if not check_dependencies():
        print("\n依赖检查失败，程序无法启动")
        input("按回车键退出...")
        sys.exit(1)

    # 2. 设置环境
    if not setup_environment():
        print("\n环境设置失败，程序无法启动")
        input("按回车键退出...")
        sys.exit(1)

    # 3. 检查必要文件
    if not check_required_files():
        print("\n文件检查失败，程序无法启动")
        input("按回车键退出...")
        sys.exit(1)

    # 4. 初始化日志系统
    logger = setup_app_logging()
    if not logger:
        print("\n日志系统初始化失败，程序无法启动")
        input("按回车键退出...")
        sys.exit(1)

    # 5. 设置异常处理
    setup_exception_handler()

    # 主应用逻辑
    try:
        print("\n" + "=" * 50)
        print("开始启动图形界面...")

        # 创建Qt应用实例
        app = create_application()
        if not app:
            logger.error("无法创建应用程序实例，退出")
            print("Qt应用程序创建失败")
            input("按回车键退出...")
            sys.exit(1)

        print("✓ Qt应用程序创建成功")

        # 初始化核心管理器
        print("正在初始化核心管理器...")
        logger.info("初始化核心管理器...")

        try:
            config_manager = ConfigManager()
            print("✓ 配置管理器初始化成功")
        except Exception as e:
            logger.error(f"配置管理器初始化失败: {e}")
            print(f"✗ 配置管理器初始化失败: {e}")
            input("按回车键退出...")
            sys.exit(1)

        try:
            db_manager = DatabaseManager(config_manager=config_manager)
            print("✓ 数据库管理器初始化成功")
        except Exception as e:
            logger.error(f"数据库管理器初始化失败: {e}")
            print(f"✗ 数据库管理器初始化失败: {e}")
            input("按回车键退出...")
            sys.exit(1)

        try:
            dynamic_table_manager = DynamicTableManager(db_manager=db_manager, config_manager=config_manager)
            print("✓ 动态表管理器初始化成功")
        except Exception as e:
            logger.error(f"动态表管理器初始化失败: {e}")
            print(f"✗ 动态表管理器初始化失败: {e}")
            input("按回车键退出...")
            sys.exit(1)

        logger.info("核心管理器初始化完成。")
        print("✓ 所有核心管理器初始化完成")

        # 创建并显示主窗口
        print("正在创建主窗口...")
        logger.info("开始创建主窗口...")

        # 这个变量必须在这里定义，以防止窗口被垃圾回收
        main_window = create_main_window(config_manager, db_manager, dynamic_table_manager)
        if not main_window:
            logger.error("无法创建主窗口，应用程序退出")
            print("✗ 主窗口创建失败")
            input("按回车键退出...")
            sys.exit(1)

        print("✓ 主窗口创建成功")
        print("✓ 系统启动完成，界面已显示")

        # 启动事件循环
        logger.info("启动应用程序事件循环...")
        print("\n系统运行中... (关闭窗口退出)")
        exit_code = app.exec_()
        logger.info(f"应用程序正常退出，退出代码: {exit_code}")
        print(f"应用程序正常退出，退出代码: {exit_code}")
        sys.exit(exit_code)

    except SystemExit as e:
        # 捕获sys.exit()，确保正常退出
        if logger:
            logger.info(f"应用程序通过 SystemExit 退出，代码: {e.code}")
        print(f"程序退出，代码: {e.code}")
        # 不需要重新调用sys.exit(e.code)，因为原始的sys.exit()已经触发了退出流程

    except Exception as e:
        error_msg = f"应用程序启动时发生致命错误: {e}"
        print("\n" + "=" * 50)
        print("程序启动失败!")
        print("=" * 50)
        print(f"错误信息: {error_msg}")
        print("\n详细错误信息:")
        traceback.print_exc()

        if logger:
            logger.critical(error_msg, exc_info=True)

        print("\n请检查以下可能的问题:")
        print("1. 是否安装了所有必要的依赖包")
        print("2. 配置文件是否正确")
        print("3. 数据库文件是否可访问")
        print("4. 系统是否支持图形界面")
        print("\n如需帮助，请查看日志文件: logs/salary_system.log")
        input("\n按回车键退出...")
        sys.exit(1)