#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的窗体测试程序
用于验证PyQt5基本功能是否正常
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_pyqt():
    """测试基本的PyQt5功能"""
    try:
        print("正在测试PyQt5基本功能...")
        
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        
        print("✓ PyQt5导入成功")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        print("✓ QApplication创建成功")
        
        # 创建主窗口
        window = QMainWindow()
        window.setWindowTitle("PyQt5测试窗口")
        window.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("PyQt5测试成功！\n如果你能看到这个窗口，说明PyQt5工作正常。")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        print("✓ 窗口组件创建成功")
        
        # 显示窗口
        window.show()
        print("✓ 窗口显示成功")
        
        print("\n窗口已显示，请检查是否能看到测试窗口")
        print("关闭窗口或按Ctrl+C退出")
        
        # 运行事件循环
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"PyQt5测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dependencies():
    """测试依赖项"""
    print("正在检查依赖项...")
    
    try:
        import PyQt5
        print("✓ PyQt5可用")
    except ImportError as e:
        print(f"✗ PyQt5不可用: {e}")
        return False
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5.QtWidgets可用")
    except ImportError as e:
        print(f"✗ PyQt5.QtWidgets不可用: {e}")
        return False
    
    try:
        from PyQt5.QtCore import Qt
        print("✓ PyQt5.QtCore可用")
    except ImportError as e:
        print(f"✗ PyQt5.QtCore不可用: {e}")
        return False
    
    try:
        from PyQt5.QtGui import QFont
        print("✓ PyQt5.QtGui可用")
    except ImportError as e:
        print(f"✗ PyQt5.QtGui不可用: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("PyQt5简化测试程序")
    print("=" * 50)
    
    # 检查依赖
    if not test_dependencies():
        print("\n依赖检查失败，无法继续测试")
        input("按回车键退出...")
        sys.exit(1)
    
    print("\n依赖检查通过，开始测试基本窗口功能...")
    print("=" * 50)
    
    # 测试基本PyQt功能
    test_basic_pyqt()
