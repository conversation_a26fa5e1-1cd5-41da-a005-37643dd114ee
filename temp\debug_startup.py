#!/usr/bin/env python3
"""
诊断GUI启动问题的调试脚本

主要目标：
1. 确认QApplication是否正确创建
2. 确认主窗口是否正确创建
3. 确认窗口是否正确显示
4. 确认事件循环是否正确启动
"""

import sys
import traceback
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_qt_creation():
    """测试Qt应用创建"""
    print("1. 测试QApplication创建...")
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        print("   ✓ QApplication创建成功")
        return app
    except Exception as e:
        print(f"   ✗ QApplication创建失败: {e}")
        traceback.print_exc()
        return None

def test_core_managers():
    """测试核心管理器创建"""
    print("2. 测试核心管理器创建...")
    try:
        from src.modules.system_config.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        config_manager = ConfigManager()
        print("   ✓ ConfigManager创建成功")
        
        db_manager = DatabaseManager(config_manager=config_manager)
        print("   ✓ DatabaseManager创建成功")
        
        dynamic_table_manager = DynamicTableManager(db_manager=db_manager, config_manager=config_manager)
        print("   ✓ DynamicTableManager创建成功")
        
        return config_manager, db_manager, dynamic_table_manager
    except Exception as e:
        print(f"   ✗ 核心管理器创建失败: {e}")
        traceback.print_exc()
        return None, None, None

def test_main_window_creation(config_manager, db_manager, dynamic_table_manager):
    """测试主窗口创建"""
    print("3. 测试主窗口创建...")
    try:
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        
        main_window = PrototypeMainWindow(
            config_manager=config_manager,
            db_manager=db_manager,
            dynamic_table_manager=dynamic_table_manager
        )
        print("   ✓ PrototypeMainWindow创建成功")
        return main_window
    except Exception as e:
        print(f"   ✗ 主窗口创建失败: {e}")
        traceback.print_exc()
        return None

def test_window_show(main_window):
    """测试窗口显示"""
    print("4. 测试窗口显示...")
    try:
        main_window.show()
        print("   ✓ 窗口show()调用成功")
        
        # 检查窗口是否可见
        if main_window.isVisible():
            print("   ✓ 窗口可见状态确认")
        else:
            print("   ⚠ 窗口不可见（可能是正常的，取决于平台）")
        
        return True
    except Exception as e:
        print(f"   ✗ 窗口显示失败: {e}")
        traceback.print_exc()
        return False

def test_event_loop(app):
    """测试事件循环（5秒后自动退出）"""
    print("5. 测试事件循环（5秒后自动退出）...")
    try:
        from PyQt5.QtCore import QTimer
        
        # 设置5秒后自动退出
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(5000)  # 5秒
        
        print("   ✓ 启动事件循环，5秒后自动退出...")
        exit_code = app.exec_()
        print(f"   ✓ 事件循环正常退出，退出代码: {exit_code}")
        return True
    except Exception as e:
        print(f"   ✗ 事件循环失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== GUI启动诊断测试 ===\n")
    
    # 1. 测试QApplication创建
    app = test_qt_creation()
    if not app:
        print("\n❌ 测试失败：无法创建QApplication")
        return False
    
    # 2. 测试核心管理器创建
    config_manager, db_manager, dynamic_table_manager = test_core_managers()
    if not all([config_manager, db_manager, dynamic_table_manager]):
        print("\n❌ 测试失败：无法创建核心管理器")
        return False
    
    # 3. 测试主窗口创建
    main_window = test_main_window_creation(config_manager, db_manager, dynamic_table_manager)
    if not main_window:
        print("\n❌ 测试失败：无法创建主窗口")
        return False
    
    # 4. 测试窗口显示
    if not test_window_show(main_window):
        print("\n❌ 测试失败：无法显示窗口")
        return False
    
    # 5. 测试事件循环
    if not test_event_loop(app):
        print("\n❌ 测试失败：事件循环异常")
        return False
    
    print("\n✅ 所有测试通过！GUI系统正常工作")
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {e}")
        traceback.print_exc()
        sys.exit(1) 