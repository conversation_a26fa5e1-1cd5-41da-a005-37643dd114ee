<!-- Generated by SpecStory -->

# GUI启动诊断测试结果 (2025-06-25 16:25:32)

_**User**_

测试脚本运行结果如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python temp/debug_startup.py
=== GUI启动诊断测试 ===

1. 测试QApplication创建...
   ✓ QApplication创建成功
2. 测试核心管理器创建...
2025-06-25 16:26:03.101 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-25 16:26:03.101 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-25 16:26:03.101 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-25 16:26:03.101 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-25 16:26:03.101 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary
_changes\salary_changes\logs\salary_system.log
2025-06-25 16:26:03.101 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-25 16:26:03.733 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配
置文件: C:\test\salary_changes\salary_changes\config.json
   ✓ ConfigManager创建成功
2025-06-25 16:26:03.733 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\
test\salary_changes\salary_changes\config.json
2025-06-25 16:26:03.733 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置
文件验证通过
2025-06-25 16:26:03.733 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-25 16:26:03.733 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化
数据库...
2025-06-25 16:26:03.749 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始
化完成
2025-06-25 16:26:03.749 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，
数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
   ✓ DatabaseManager创建成功
2025-06-25 16:26:03.749 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化
完成
   ✓ DynamicTableManager创建成功
3. 测试主窗口创建...
2025-06-25 16:26:04.480 | INFO     | src.gui.prototype.prototype_main_window:__init__:1906 | PrototypeMainWindow: 正
在初始化...
2025-06-25 16:26:04.480 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成
 - 最大条目数: 50, 最大内存: 50MB
2025-06-25 16:26:04.480 | INFO     | src.gui.prototype.prototype_main_window:__init__:1918 | PrototypeMainWindow: 分
页缓存管理器初始化完成
2025-06-25 16:26:04.480 | INFO     | src.gui.prototype.prototype_main_window:__init__:1923 | PrototypeMainWindow: 线
程池初始化完成，最大线程数: 4
2025-06-25 16:26:04.480 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管
理器初始化完成
2025-06-25 16:26:04.480 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初
始化完成
2025-06-25 16:26:04.718 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1362 | 菜单栏创建完成
2025-06-25 16:26:04.718 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始
化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-25 16:26:04.718 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设
置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-25 16:26:04.718 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成
功
2025-06-25 16:26:04.718 | INFO     | src.gui.prototype.prototype_main_window:__init__:1338 | 菜单栏管理器初始化完成
2025-06-25 16:26:04.718 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-25 16:26:04.718 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2027 | 管理器设置完成，
包含增强版表头管理器
2025-06-25 16:26:04.718 | INFO     | src.gui.prototype.prototype_main_window:__init__:1927 | PrototypeMainWindow: _se
tup_managers() 完成
2025-06-25 16:26:04.718 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加
载成功
2025-06-25 16:26:04.718 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法
初始化完成
2025-06-25 16:26:04.734 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: st
ate/user/tree_expansion_data.json
2025-06-25 16:26:04.734 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初
始化完成
2025-06-25 16:26:04.756 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:91
6 | 开始从元数据动态加载工资数据...
2025-06-25 16:26:04.757 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:92
3 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-25 16:26:04.761 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 |
 导航面板已重构：移除功能性导航，专注数据导航
2025-06-25 16:26:04.777 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导
航状态: 75个展开项
2025-06-25 16:26:04.783 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预
测展开路径: ['工资表 > 2025年', '异动人员表', '工资表', '异动人员表 > 2025年', '异动人员表 > 2025年 > 5月']
2025-06-25 16:26:04.785 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板
初始化完成
2025-06-25 16:26:04.814 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成
 - 最大条目数: 10, 最大内存: 5MB
2025-06-25 16:26:04.821 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
Material标签切换到: 数据概览 (索引: 0)
2025-06-25 16:26:04.850 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:373 | 控
制面板按钮信号连接完成
2025-06-25 16:26:04.851 | INFO     | src.gui.prototype.prototype_main_window:__init__:355 | 主工作区初始化完成，线程
池已准备就绪。
2025-06-25 16:26:04.852 | INFO     | src.gui.prototype.prototype_main_window:__init__:356 | 线程池最大线程数: 4
2025-06-25 16:26:04.871 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配
置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-25 16:26:04.872 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始
化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-25 16:26:04.873 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2009 | 快捷键设置完成
2025-06-25 16:26:04.873 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1998 | 主窗口UI设置完成。
2025-06-25 16:26:04.873 | INFO     | src.gui.prototype.prototype_main_window:__init__:1931 | PrototypeMainWindow: _se
tup_ui() 完成
2025-06-25 16:26:04.874 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2055 | 信号连接设置完
成
2025-06-25 16:26:04.875 | INFO     | src.gui.prototype.prototype_main_window:__init__:1935 | PrototypeMainWindow: _se
tup_connections() 完成
2025-06-25 16:26:04.877 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完
成
2025-06-25 16:26:04.917 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2480 | 已
加载字段映射信息，共42个表的映射
2025-06-25 16:26:04.919 | INFO     | src.gui.prototype.prototype_main_window:__init__:1939 | PrototypeMainWindow: _lo
ad_field_mappings_from_file() 完成
2025-06-25 16:26:04.926 | WARNING  | src.gui.prototype.prototype_main_window:set_data:454 | 尝试设置空数据，将显示提
示信息。
2025-06-25 16:26:04.926 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:975 | 首次
创建表格实例
2025-06-25 16:26:04.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1263
| 快捷键注册完成: 18/18 个
2025-06-25 16:26:04.931 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1474 | 拖拽排序
管理器初始化完成
2025-06-25 16:26:04.932 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完
成
2025-06-25 16:26:04.933 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完
成
2025-06-25 16:26:04.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1856 | 虚拟化可
展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-25 16:26:04.941 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:984 | 表格
已初始化为空白状态，等待用户导入数据
2025-06-25 16:26:04.942 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_r
ows:2671 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-25 16:26:04.942 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已
设置: 0 行, 0 列
2025-06-25 16:26:04.945 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_auto_generate_field_mapp
ing_if_needed:2046 | 表 default_table 需要自动生成字段映射: 覆盖率=0.0%, 中文映射=0/0
2025-06-25 16:26:04.948 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:64 | 自动字段映射
生成器初始化完成
2025-06-25 16:26:04.949 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_columns_with_descriptio
ns:628 | 无法获取表 default_table 的结构信息，可能表不存在
2025-06-25 16:26:04.951 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping_from_db_fi
elds:156 | 开始为表 'default_table' 基于数据库字段生成映射，共 0 个字段
2025-06-25 16:26:04.953 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping_from_db_fi
elds:224 | 基于数据库字段的映射生成完成，详细信息:
2025-06-25 16:26:04.953 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping_from_db_fi
elds:230 | 映射质量评估: 覆盖率 0.0%, 中文映射 0/0
2025-06-25 16:26:04.954 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_auto_generate_field_mapp
ing_if_needed:2079 | 为表 default_table 生成的字段映射为空，跳过更新。

---

_**Assistant**_



---

Read file: temp/debug_startup.py

---



---

<details>
            <summary>Grep search for "class AutoFieldMappingGenerator" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_import/auto_field_mapping_generator.py` | L32 | `class AutoFieldMappingGenerator:` |

</details>

---

Read file: src/modules/data_import/auto_field_mapping_generator.py

---



---

<details>
            <summary>Grep search for "_auto_generate_field_mapping_if_needed" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L1987 | `self._auto_generate_field_mapping_if_needed(headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2007 | `def _auto_generate_field_mapping_if_needed(self, headers: List[str]):` |

</details>

---

Read file: src/gui/prototype/widgets/virtualized_expandable_table.py

---



---

<details>
            <summary>Grep search for "def get_table_columns_with_descriptions" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L618 | `def get_table_columns_with_descriptions(self, table_name: str) -> Tuple[List[str], Dict[str, str]]:` |

</details>

---

Read file: src/modules/data_storage/dynamic_table_manager.py

---



---

<details>
            <summary>Grep search for "def set_data(self," • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L450 | `def set_data(self, df: Optional[pd.DataFrame], preserve_headers: bool = False, table_name: str = ""):` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L450 | `def set_data(self, df: Optional[pd.DataFrame], preserve_headers: bool = False, table_name: str = ""):` |

</details>

---



---

```diff
          """
          try:
+             # 如果表头为空，直接返回，避免在无字段情况下执行耗时操作
+             if not headers:
+                 self.logger.debug("表头为空，跳过自动字段映射生成。")
+                 return
+ 
              if not hasattr(self, 'current_table_name') or not self.current_table_name:
                  return
```

---

我已在 `src/gui/prototype/widgets/virtualized_expandable_table.py` 的 `_auto_generate_field_mapping_if_needed` 方法前端增加了「空表头快速退出」逻辑：

```python
# 如果表头为空，直接返回，避免在无字段情况下执行耗时操作
if not headers:
    self.logger.debug("表头为空，跳过自动字段映射生成。")
    return
```

这样在启动阶段无数据或空表头时，将不会触发字段映射的初始化流程，可显著缩短主窗口 `__init__` 的执行时间，避免阻塞 GUI 初始化。请重新运行 `temp/debug_startup.py` 观察是否能够顺利打印出 “✓ PrototypeMainWindow创建成功” 及后续步骤。

---

_**User**_

又有错误：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
2025-06-25 16:44:18.957 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-25 16:44:18.957 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-25 16:44:18.957 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-25 16:44:18.957 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-25 16:44:18.957 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary
_changes\salary_changes\logs\salary_system.log
2025-06-25 16:44:18.957 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-25 16:44:20.279 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
正在启动 月度工资异动处理系统 v2.0.0-refactored...
2025-06-25 16:44:20.295 | INFO     | __main__:<module>:295 | 初始化核心管理器...
2025-06-25 16:44:20.295 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配
置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-25 16:44:20.295 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\
test\salary_changes\salary_changes\config.json
2025-06-25 16:44:20.295 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置
文件验证通过
2025-06-25 16:44:20.295 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-25 16:44:20.295 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化
数据库...
2025-06-25 16:44:20.313 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始
化完成
2025-06-25 16:44:20.313 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，
数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-25 16:44:20.314 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化
完成
2025-06-25 16:44:20.315 | INFO     | __main__:<module>:299 | 核心管理器初始化完成。
2025-06-25 16:44:20.315 | INFO     | src.gui.prototype.prototype_main_window:__init__:1906 | PrototypeMainWindow: 正
在初始化...
2025-06-25 16:44:20.316 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成
 - 最大条目数: 50, 最大内存: 50MB
2025-06-25 16:44:20.316 | INFO     | src.gui.prototype.prototype_main_window:__init__:1918 | PrototypeMainWindow: 分
页缓存管理器初始化完成
2025-06-25 16:44:20.317 | INFO     | src.gui.prototype.prototype_main_window:__init__:1923 | PrototypeMainWindow: 线
程池初始化完成，最大线程数: 4
2025-06-25 16:44:20.317 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管
理器初始化完成
2025-06-25 16:44:20.318 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初
始化完成
2025-06-25 16:44:20.563 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1362 | 菜单栏创建完成
2025-06-25 16:44:20.563 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始
化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-25 16:44:20.563 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设
置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-25 16:44:20.563 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成
功
2025-06-25 16:44:20.563 | INFO     | src.gui.prototype.prototype_main_window:__init__:1338 | 菜单栏管理器初始化完成
2025-06-25 16:44:20.563 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-25 16:44:20.563 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2027 | 管理器设置完成，
包含增强版表头管理器
2025-06-25 16:44:20.563 | INFO     | src.gui.prototype.prototype_main_window:__init__:1927 | PrototypeMainWindow: _se
tup_managers() 完成
2025-06-25 16:44:20.563 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加
载成功
2025-06-25 16:44:20.563 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法
初始化完成
2025-06-25 16:44:20.595 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: st
ate/user/tree_expansion_data.json
2025-06-25 16:44:20.595 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初
始化完成
2025-06-25 16:44:20.595 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:91
6 | 开始从元数据动态加载工资数据...
2025-06-25 16:44:20.595 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:92
3 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-25 16:44:20.610 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 |
 导航面板已重构：移除功能性导航，专注数据导航
2025-06-25 16:44:20.622 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导
航状态: 75个展开项
2025-06-25 16:44:20.630 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预
测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '工资表 > 2024年', '异动人员表 > 2024年']
2025-06-25 16:44:20.630 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板
初始化完成
2025-06-25 16:44:20.661 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成
 - 最大条目数: 10, 最大内存: 5MB
2025-06-25 16:44:20.671 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
Material标签切换到: 数据概览 (索引: 0)
2025-06-25 16:44:20.700 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:373 | 控
制面板按钮信号连接完成
2025-06-25 16:44:20.702 | INFO     | src.gui.prototype.prototype_main_window:__init__:355 | 主工作区初始化完成，线程
池已准备就绪。
2025-06-25 16:44:20.703 | INFO     | src.gui.prototype.prototype_main_window:__init__:356 | 线程池最大线程数: 4
2025-06-25 16:44:20.719 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配
置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-25 16:44:20.720 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始
化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-25 16:44:20.722 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2009 | 快捷键设置完成
2025-06-25 16:44:20.723 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1998 | 主窗口UI设置完成。
2025-06-25 16:44:20.724 | INFO     | src.gui.prototype.prototype_main_window:__init__:1931 | PrototypeMainWindow: _se
tup_ui() 完成
2025-06-25 16:44:20.724 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2055 | 信号连接设置完
成
2025-06-25 16:44:20.728 | INFO     | src.gui.prototype.prototype_main_window:__init__:1935 | PrototypeMainWindow: _se
tup_connections() 完成
2025-06-25 16:44:20.730 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完
成
2025-06-25 16:44:20.776 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2480 | 已
加载字段映射信息，共42个表的映射
2025-06-25 16:44:20.777 | INFO     | src.gui.prototype.prototype_main_window:__init__:1939 | PrototypeMainWindow: _lo
ad_field_mappings_from_file() 完成
2025-06-25 16:44:20.785 | WARNING  | src.gui.prototype.prototype_main_window:set_data:454 | 尝试设置空数据，将显示提
示信息。
2025-06-25 16:44:20.786 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:975 | 首次
创建表格实例
2025-06-25 16:44:20.789 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1263
| 快捷键注册完成: 18/18 个
2025-06-25 16:44:20.790 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1474 | 拖拽排序
管理器初始化完成
2025-06-25 16:44:20.791 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完
成
2025-06-25 16:44:20.792 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完
成
2025-06-25 16:44:20.795 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1856 | 虚拟化可
展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-25 16:44:20.797 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:984 | 表格
已初始化为空白状态，等待用户导入数据
2025-06-25 16:44:20.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_r
ows:2676 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-25 16:44:20.799 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已
设置: 0 行, 0 列

---

_**Assistant**_



---

Read file: main.py

---

Read file: main.py

---



---

Read file: logs/salary_system.log

---



---

Read file: logs/salary_system.log

---



---

```diff
                  return
  
+             # 如果当前表名为 default_table（空表），也跳过自动映射生成
+             if getattr(self, 'current_table_name', '') == 'default_table':
+                 self.logger.debug("当前表为 default_table，跳过自动字段映射生成。")
+                 return
+             
              if not hasattr(self, 'current_table_name') or not self.current_table_name:
                  return
```

---

已进一步增强 `_auto_generate_field_mapping_if_needed` 的保护逻辑：

1. 表头 `headers` 为空时直接返回（已添加）。
2. 若 `current_table_name == "default_table"`（启动阶段的占位空表）也立即返回，防止无意义的字段映射流程。

这样主窗口首次启动不会触发任何字段映射计算，进一步减少初始化耗时并避免日志杂讯。  
请再次运行 `python main.py` 验证启动流程，若仍出现问题，请提供完整错误或异常堆栈以便定位。

---

_**User**_

又出错：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
2025-06-25 16:48:00.246 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-25 16:48:00.247 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-25 16:48:00.247 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-25 16:48:00.248 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-25 16:48:00.248 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary
_changes\salary_changes\logs\salary_system.log
2025-06-25 16:48:00.249 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-25 16:48:01.602 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
正在启动 月度工资异动处理系统 v2.0.0-refactored...
2025-06-25 16:48:01.612 | INFO     | __main__:<module>:295 | 初始化核心管理器...
2025-06-25 16:48:01.612 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配
置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-25 16:48:01.613 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\
test\salary_changes\salary_changes\config.json
2025-06-25 16:48:01.614 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置
文件验证通过
2025-06-25 16:48:01.614 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-25 16:48:01.615 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化
数据库...
2025-06-25 16:48:01.622 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始
化完成
2025-06-25 16:48:01.623 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，
数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-25 16:48:01.623 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化
完成
2025-06-25 16:48:01.624 | INFO     | __main__:<module>:299 | 核心管理器初始化完成。
2025-06-25 16:48:01.624 | INFO     | src.gui.prototype.prototype_main_window:__init__:1906 | PrototypeMainWindow: 正
在初始化...
2025-06-25 16:48:01.624 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成
 - 最大条目数: 50, 最大内存: 50MB
2025-06-25 16:48:01.624 | INFO     | src.gui.prototype.prototype_main_window:__init__:1918 | PrototypeMainWindow: 分
页缓存管理器初始化完成
2025-06-25 16:48:01.627 | INFO     | src.gui.prototype.prototype_main_window:__init__:1923 | PrototypeMainWindow: 线
程池初始化完成，最大线程数: 4
2025-06-25 16:48:01.627 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管
理器初始化完成
2025-06-25 16:48:01.628 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初
始化完成
2025-06-25 16:48:01.885 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1362 | 菜单栏创建完成
2025-06-25 16:48:01.886 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始
化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-25 16:48:01.886 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设
置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-25 16:48:01.887 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成
功
2025-06-25 16:48:01.888 | INFO     | src.gui.prototype.prototype_main_window:__init__:1338 | 菜单栏管理器初始化完成
2025-06-25 16:48:01.888 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-25 16:48:01.890 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2027 | 管理器设置完成，
包含增强版表头管理器
2025-06-25 16:48:01.890 | INFO     | src.gui.prototype.prototype_main_window:__init__:1927 | PrototypeMainWindow: _se
tup_managers() 完成
2025-06-25 16:48:01.892 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加
载成功
2025-06-25 16:48:01.893 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法
初始化完成
2025-06-25 16:48:01.914 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: st
ate/user/tree_expansion_data.json
2025-06-25 16:48:01.915 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初
始化完成
2025-06-25 16:48:01.927 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:91
6 | 开始从元数据动态加载工资数据...
2025-06-25 16:48:01.928 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:92
3 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-25 16:48:01.930 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 |
 导航面板已重构：移除功能性导航，专注数据导航
2025-06-25 16:48:01.948 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导
航状态: 75个展开项
2025-06-25 16:48:01.956 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预
测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年', '异动人员表 > 2025年 > 4月']
2025-06-25 16:48:01.957 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板
初始化完成
2025-06-25 16:48:01.990 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成
 - 最大条目数: 10, 最大内存: 5MB
2025-06-25 16:48:02.001 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
Material标签切换到: 数据概览 (索引: 0)
2025-06-25 16:48:02.027 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:373 | 控
制面板按钮信号连接完成
2025-06-25 16:48:02.030 | INFO     | src.gui.prototype.prototype_main_window:__init__:355 | 主工作区初始化完成，线程
池已准备就绪。
2025-06-25 16:48:02.033 | INFO     | src.gui.prototype.prototype_main_window:__init__:356 | 线程池最大线程数: 4
2025-06-25 16:48:02.050 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配
置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-25 16:48:02.051 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始
化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-25 16:48:02.052 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2009 | 快捷键设置完成
2025-06-25 16:48:02.052 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1998 | 主窗口UI设置完成。
2025-06-25 16:48:02.055 | INFO     | src.gui.prototype.prototype_main_window:__init__:1931 | PrototypeMainWindow: _se
tup_ui() 完成
2025-06-25 16:48:02.056 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2055 | 信号连接设置完
成
2025-06-25 16:48:02.058 | INFO     | src.gui.prototype.prototype_main_window:__init__:1935 | PrototypeMainWindow: _se
tup_connections() 完成
2025-06-25 16:48:02.060 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完
成
2025-06-25 16:48:02.107 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2480 | 已
加载字段映射信息，共42个表的映射
2025-06-25 16:48:02.108 | INFO     | src.gui.prototype.prototype_main_window:__init__:1939 | PrototypeMainWindow: _lo
ad_field_mappings_from_file() 完成
2025-06-25 16:48:02.109 | WARNING  | src.gui.prototype.prototype_main_window:set_data:454 | 尝试设置空数据，将显示提
示信息。
2025-06-25 16:48:02.110 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:975 | 首次
创建表格实例
2025-06-25 16:48:02.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1263
| 快捷键注册完成: 18/18 个
2025-06-25 16:48:02.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1474 | 拖拽排序
管理器初始化完成
2025-06-25 16:48:02.116 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完
成
2025-06-25 16:48:02.117 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完
成
2025-06-25 16:48:02.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1856 | 虚拟化可
展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-25 16:48:02.125 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:984 | 表格
已初始化为空白状态，等待用户导入数据
2025-06-25 16:48:02.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_r
ows:2681 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-25 16:48:02.126 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已
设置: 0 行, 0 列

---

_**Assistant**_



---

